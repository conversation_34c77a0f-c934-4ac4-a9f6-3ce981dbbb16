<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Vue liste des crédits budgétaires -->
        <record id="view_budget_credit_tree" model="ir.ui.view">
            <field name="name">budget.credit.tree</field>
            <field name="model">budget.credit</field>
            <field name="arch" type="xml">
                <tree string="Crédits Budgétaires" decoration-info="state=='draft'"
                      decoration-success="state=='validated'" decoration-danger="state=='blocked'">
                    <field name="exercise_id"/>
                    <field name="nomenclature_id"/>
                    <field name="amount_initial" widget="monetary"/>
                    <field name="amount_adjustments" widget="monetary"/>
                    <field name="amount_voted" widget="monetary"/>
                    <field name="amount_engaged" widget="monetary"/>
                    <field name="amount_available" widget="monetary"
                           decoration-danger="amount_available &lt; 0"/>
                    <field name="consumption_rate" widget="percentage"
                           decoration-warning="consumption_rate &gt;= 80 and consumption_rate &lt; 100"
                           decoration-danger="consumption_rate &gt;= 100"/>
                    <field name="payment_rate" widget="percentage"/>
                    <field name="state" widget="badge"
                           decoration-info="state=='draft'"
                           decoration-success="state=='validated'"
                           decoration-danger="state=='blocked'"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!-- Vue formulaire des crédits budgétaires -->
        <record id="view_budget_credit_form" model="ir.ui.view">
            <field name="name">budget.credit.form</field>
            <field name="model">budget.credit</field>
            <field name="arch" type="xml">
                <form string="Crédit Budgétaire">
                    <header>
                        <button name="action_validate" string="Valider" type="object"
                                class="oe_highlight" invisible="state != 'draft'"
                                groups="gestion_budgetaire.group_budget_agent"/>
                        <button name="action_block" string="Bloquer" type="object"
                                invisible="state != 'validated'"
                                groups="gestion_budgetaire.group_budget_controller"/>
                        <button name="action_unblock" string="Débloquer" type="object"
                                invisible="state != 'blocked'"
                                groups="gestion_budgetaire.group_budget_controller"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,validated,blocked"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_adjustments" type="object"
                                    class="oe_stat_button" icon="fa-exchange">
                                <div class="o_stat_info">
                                    <span class="o_stat_text">Ajustements</span>
                                </div>
                            </button>
                            <button name="action_view_engagements" type="object"
                                    class="oe_stat_button" icon="fa-file-text-o">
                                <div class="o_stat_info">
                                    <span class="o_stat_text">Engagements</span>
                                </div>
                            </button>
                        </div>

                        <widget name="web_ribbon" title="Bloqué" bg_color="bg-danger"
                                invisible="not is_blocked"/>

                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>

                        <group>
                            <group>
                                <field name="exercise_id" readonly="state != 'draft'"/>
                                <field name="nomenclature_id" readonly="state != 'draft'"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                                <field name="currency_id" invisible="1"/>
                            </group>
                            <group>
                                <field name="state" invisible="1"/>
                                <field name="is_blocked"/>
                                <field name="blocking_reason" invisible="not is_blocked"/>
                            </group>
                        </group>

                        <group string="Montants">
                            <group>
                                <field name="amount_initial" widget="monetary"/>
                                <field name="amount_adjustments" widget="monetary"/>
                                <field name="amount_voted" widget="monetary"/>
                            </group>
                            <group>
                                <field name="amount_engaged" widget="monetary"/>
                                <field name="amount_mandated" widget="monetary"/>
                                <field name="amount_available" widget="monetary"
                                       decoration-danger="amount_available &lt; 0"/>
                                <field name="amount_to_pay" widget="monetary"/>
                            </group>
                        </group>

                        <group string="Indicateurs">
                            <group>
                                <field name="consumption_rate" widget="percentage"
                                       decoration-warning="consumption_rate &gt;= 80 and consumption_rate &lt; 100"
                                       decoration-danger="consumption_rate &gt;= 100"/>
                                <field name="payment_rate" widget="percentage"/>
                            </group>
                            <group>
                                <field name="alert_threshold"/>
                                <field name="blocking_threshold"/>
                            </group>
                        </group>

                        <field name="notes" placeholder="Notes et commentaires"/>

                        <notebook>
                            <page string="Ajustements" name="adjustments">
                                <field name="adjustment_line_ids" readonly="1">
                                    <tree>
                                        <field name="adjustment_id"/>
                                        <field name="amount" widget="monetary"/>
                                        <field name="adjustment_type"/>
                                        <field name="currency_id" invisible="1"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Engagements" name="engagements">
                                <field name="engagement_ids" readonly="1">
                                    <tree>
                                        <field name="name"/>
                                        <field name="date"/>
                                        <field name="partner_id"/>
                                        <field name="amount" widget="monetary"/>
                                        <field name="amount_mandated" widget="monetary"/>
                                        <field name="amount_remaining" widget="monetary"/>
                                        <field name="state" widget="badge"/>
                                        <field name="currency_id" invisible="1"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Vue kanban des crédits budgétaires -->
        <record id="view_budget_credit_kanban" model="ir.ui.view">
            <field name="name">budget.credit.kanban</field>
            <field name="model">budget.credit</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="name"/>
                    <field name="nomenclature_id"/>
                    <field name="amount_voted"/>
                    <field name="amount_engaged"/>
                    <field name="amount_available"/>
                    <field name="consumption_rate"/>
                    <field name="state"/>
                    <field name="currency_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="nomenclature_id"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="name"/>
                                        </small>
                                    </div>
                                    <span class="o_kanban_record_top_right">
                                        <field name="state" widget="label_selection"
                                               options="{'classes': {'draft': 'info', 'validated': 'success', 'blocked': 'danger'}}"/>
                                    </span>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-6">
                                            <span>Voté:</span><br/>
                                            <strong><field name="amount_voted" widget="monetary"/></strong>
                                        </div>
                                        <div class="col-6">
                                            <span>Engagé:</span><br/>
                                            <strong><field name="amount_engaged" widget="monetary"/></strong>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-6">
                                            <span>Disponible:</span><br/>
                                            <strong><field name="amount_available" widget="monetary"/></strong>
                                        </div>
                                        <div class="col-6">
                                            <span>Consommation:</span><br/>
                                            <strong><field name="consumption_rate" widget="percentage"/></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Vue recherche des crédits budgétaires -->
        <record id="view_budget_credit_search" model="ir.ui.view">
            <field name="name">budget.credit.search</field>
            <field name="model">budget.credit</field>
            <field name="arch" type="xml">
                <search string="Rechercher Crédits">
                    <field name="name"/>
                    <field name="exercise_id"/>
                    <field name="nomenclature_id"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <separator/>
                    <filter string="Brouillon" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Validé" name="validated" domain="[('state', '=', 'validated')]"/>
                    <filter string="Bloqué" name="blocked" domain="[('state', '=', 'blocked')]"/>
                    <separator/>
                    <filter string="Crédit disponible" name="available" domain="[('amount_available', '>', 0)]"/>
                    <filter string="Crédit épuisé" name="exhausted" domain="[('amount_available', '&lt;=', 0)]"/>
                    <filter string="Seuil d'alerte" name="alert" domain="[('consumption_rate', '&gt;=', 80)]"/>
                    <filter string="Dépassement" name="exceeded" domain="[('consumption_rate', '&gt;=', 100)]"/>
                    <separator/>
                    <filter string="Exercice actuel" name="current_exercise"
                            domain="[('exercise_id.state', '=', 'open')]"/>
                    <separator/>
                    <group expand="0" string="Grouper par">
                        <filter string="Exercice" name="group_exercise" context="{'group_by': 'exercise_id'}"/>
                        <filter string="Poste budgétaire" name="group_nomenclature" context="{'group_by': 'nomenclature_id'}"/>
                        <filter string="État" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Société" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Action pour les crédits budgétaires -->
        <record id="action_budget_credit" model="ir.actions.act_window">
            <field name="name">Crédits Budgétaires</field>
            <field name="res_model">budget.credit</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_credit_search"/>
            <field name="context">{'search_default_current_exercise': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Créer un nouveau crédit budgétaire
                </p>
                <p>
                    Les crédits budgétaires définissent les montants alloués
                    à chaque poste budgétaire pour un exercice donné.
                </p>
                <p>
                    Ils servent de base au contrôle budgétaire lors de la
                    création des engagements.
                </p>
            </field>
        </record>

    </data>
</odoo>
