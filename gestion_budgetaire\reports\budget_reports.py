# -*- coding: utf-8 -*-

from odoo import api, fields, models, tools, _
from odoo.exceptions import ValidationError


class BudgetSituationReport(models.Model):
    """Rapport de situation des crédits budgétaires"""
    
    _name = 'budget.situation.report'
    _description = 'Rapport Situation Budgétaire'
    _auto = False
    _order = 'nomenclature_code'
    
    # Champs de base
    exercise_id = fields.Many2one('budget.exercise', string='Exercice', readonly=True)
    nomenclature_id = fields.Many2one('budget.nomenclature', string='Poste budgétaire', readonly=True)
    nomenclature_code = fields.Char(string='Code poste', readonly=True)
    nomenclature_name = fields.Char(string='Libellé poste', readonly=True)
    
    # Montants
    amount_initial = fields.Monetary(string='Crédits initiaux', readonly=True)
    amount_adjustments = fields.Monetary(string='Ajustements', readonly=True)
    amount_voted = fields.Monetary(string='Crédits votés', readonly=True)
    amount_engaged = fields.Monetary(string='Engagements', readonly=True)
    amount_mandated = fields.Monetary(string='Mandatements', readonly=True)
    amount_available = fields.Monetary(string='Crédits disponibles', readonly=True)
    amount_to_pay = fields.Monetary(string='Reste à payer', readonly=True)
    
    # Taux
    consumption_rate = fields.Float(string='Taux de consommation (%)', readonly=True)
    payment_rate = fields.Float(string='Taux de paiement (%)', readonly=True)
    
    # Devise et société
    currency_id = fields.Many2one('res.currency', string='Devise', readonly=True)
    company_id = fields.Many2one('res.company', string='Société', readonly=True)
    
    def init(self):
        """Initialiser la vue SQL"""
        tools.drop_view_if_exists(self.env.cr, self._table)
        self.env.cr.execute("""
            CREATE OR REPLACE VIEW %s AS (
                SELECT
                    row_number() OVER () AS id,
                    c.exercise_id,
                    c.nomenclature_id,
                    n.complete_code AS nomenclature_code,
                    n.name AS nomenclature_name,
                    c.amount_initial,
                    c.amount_adjustments,
                    c.amount_voted,
                    c.amount_engaged,
                    c.amount_mandated,
                    c.amount_available,
                    c.amount_to_pay,
                    c.consumption_rate,
                    c.payment_rate,
                    c.currency_id,
                    c.company_id
                FROM budget_credit c
                LEFT JOIN budget_nomenclature n ON c.nomenclature_id = n.id
                WHERE c.state = 'validated'
            )
        """ % self._table)


class BudgetEngagementReport(models.Model):
    """Rapport des engagements budgétaires"""
    
    _name = 'budget.engagement.report'
    _description = 'Rapport Engagements Budgétaires'
    _auto = False
    _order = 'date desc'
    
    # Champs de base
    exercise_id = fields.Many2one('budget.exercise', string='Exercice', readonly=True)
    nomenclature_id = fields.Many2one('budget.nomenclature', string='Poste budgétaire', readonly=True)
    engagement_type_id = fields.Many2one('budget.engagement.type', string='Type d\'engagement', readonly=True)
    partner_id = fields.Many2one('res.partner', string='Tiers', readonly=True)
    
    # Dates
    date = fields.Date(string='Date', readonly=True)
    month = fields.Char(string='Mois', readonly=True)
    year = fields.Integer(string='Année', readonly=True)
    
    # Montants
    amount = fields.Monetary(string='Montant engagé', readonly=True)
    amount_mandated = fields.Monetary(string='Montant mandaté', readonly=True)
    amount_remaining = fields.Monetary(string='Reste à mandater', readonly=True)
    
    # État
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('waiting_validation', 'En attente de validation'),
        ('validated', 'Validé'),
        ('partially_mandated', 'Partiellement mandaté'),
        ('fully_mandated', 'Totalement mandaté'),
        ('cancelled', 'Annulé'),
    ], string='État', readonly=True)
    
    # Compteurs
    engagement_count = fields.Integer(string='Nombre d\'engagements', readonly=True)
    
    # Devise et société
    currency_id = fields.Many2one('res.currency', string='Devise', readonly=True)
    company_id = fields.Many2one('res.company', string='Société', readonly=True)
    
    def init(self):
        """Initialiser la vue SQL"""
        tools.drop_view_if_exists(self.env.cr, self._table)
        self.env.cr.execute("""
            CREATE OR REPLACE VIEW %s AS (
                SELECT
                    row_number() OVER () AS id,
                    e.exercise_id,
                    e.nomenclature_id,
                    e.engagement_type_id,
                    e.partner_id,
                    e.date,
                    to_char(e.date, 'YYYY-MM') AS month,
                    EXTRACT(year FROM e.date) AS year,
                    SUM(e.amount) AS amount,
                    SUM(e.amount_mandated) AS amount_mandated,
                    SUM(e.amount_remaining) AS amount_remaining,
                    e.state,
                    COUNT(*) AS engagement_count,
                    e.currency_id,
                    e.company_id
                FROM budget_engagement e
                GROUP BY
                    e.exercise_id,
                    e.nomenclature_id,
                    e.engagement_type_id,
                    e.partner_id,
                    e.date,
                    e.state,
                    e.currency_id,
                    e.company_id
            )
        """ % self._table)


class BudgetMandateReport(models.Model):
    """Rapport des mandats de paiement"""
    
    _name = 'budget.mandate.report'
    _description = 'Rapport Mandats de Paiement'
    _auto = False
    _order = 'date desc'
    
    # Champs de base
    exercise_id = fields.Many2one('budget.exercise', string='Exercice', readonly=True)
    nomenclature_id = fields.Many2one('budget.nomenclature', string='Poste budgétaire', readonly=True)
    payment_type_id = fields.Many2one('budget.payment.type', string='Type de paiement', readonly=True)
    partner_id = fields.Many2one('res.partner', string='Bénéficiaire', readonly=True)
    
    # Dates
    date = fields.Date(string='Date', readonly=True)
    payment_date = fields.Date(string='Date de paiement', readonly=True)
    month = fields.Char(string='Mois', readonly=True)
    year = fields.Integer(string='Année', readonly=True)
    
    # Montants
    amount = fields.Monetary(string='Montant', readonly=True)
    
    # État
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('waiting_validation', 'En attente de validation'),
        ('validated', 'Validé'),
        ('paid', 'Payé'),
        ('cancelled', 'Annulé'),
    ], string='État', readonly=True)
    
    # Compteurs
    mandate_count = fields.Integer(string='Nombre de mandats', readonly=True)
    
    # Devise et société
    currency_id = fields.Many2one('res.currency', string='Devise', readonly=True)
    company_id = fields.Many2one('res.company', string='Société', readonly=True)
    
    def init(self):
        """Initialiser la vue SQL"""
        tools.drop_view_if_exists(self.env.cr, self._table)
        self.env.cr.execute("""
            CREATE OR REPLACE VIEW %s AS (
                SELECT
                    row_number() OVER () AS id,
                    m.exercise_id,
                    m.nomenclature_id,
                    m.payment_type_id,
                    m.partner_id,
                    m.date,
                    m.payment_date,
                    to_char(m.date, 'YYYY-MM') AS month,
                    EXTRACT(year FROM m.date) AS year,
                    SUM(m.amount) AS amount,
                    m.state,
                    COUNT(*) AS mandate_count,
                    m.currency_id,
                    m.company_id
                FROM budget_mandate m
                GROUP BY
                    m.exercise_id,
                    m.nomenclature_id,
                    m.payment_type_id,
                    m.partner_id,
                    m.date,
                    m.payment_date,
                    m.state,
                    m.currency_id,
                    m.company_id
            )
        """ % self._table)
