<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Vue liste des ajustements budgétaires -->
        <record id="view_budget_adjustment_tree" model="ir.ui.view">
            <field name="name">budget.adjustment.tree</field>
            <field name="model">budget.adjustment</field>
            <field name="arch" type="xml">
                <tree string="Ajustements Budgétaires" decoration-info="state=='draft'" 
                      decoration-warning="state=='waiting_validation'" decoration-success="state=='validated'"
                      decoration-muted="state=='cancelled'">
                    <field name="name"/>
                    <field name="date"/>
                    <field name="exercise_id"/>
                    <field name="adjustment_type" widget="badge"/>
                    <field name="total_debit" widget="monetary"/>
                    <field name="total_credit" widget="monetary"/>
                    <field name="balance" widget="monetary" decoration-danger="balance != 0 and adjustment_type == 'transfer'"/>
                    <field name="state" widget="badge" 
                           decoration-info="state=='draft'"
                           decoration-warning="state=='waiting_validation'"
                           decoration-success="state=='validated'"
                           decoration-muted="state=='cancelled'"/>
                    <field name="user_id" widget="many2one_avatar_user"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>
        
        <!-- Vue formulaire des ajustements budgétaires -->
        <record id="view_budget_adjustment_form" model="ir.ui.view">
            <field name="name">budget.adjustment.form</field>
            <field name="model">budget.adjustment</field>
            <field name="arch" type="xml">
                <form string="Ajustement Budgétaire">
                    <header>
                        <button name="action_submit_for_validation" string="Soumettre" type="object" 
                                class="oe_highlight" invisible="state != 'draft'"
                                groups="gestion_budgetaire.group_budget_controller"/>
                        <button name="action_validate" string="Valider" type="object" 
                                class="oe_highlight" invisible="state != 'waiting_validation'"
                                groups="gestion_budgetaire.group_budget_manager"/>
                        <button name="action_cancel" string="Annuler" type="object" 
                                invisible="state in ('validated', 'cancelled')"
                                groups="gestion_budgetaire.group_budget_manager"/>
                        <button name="action_reset_to_draft" string="Remettre en brouillon" type="object" 
                                invisible="state not in ('waiting_validation', 'cancelled')"
                                groups="gestion_budgetaire.group_budget_controller"/>
                        <field name="state" widget="statusbar" 
                               statusbar_visible="draft,waiting_validation,validated"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_credits" type="object" 
                                    class="oe_stat_button" icon="fa-money">
                                <div class="o_stat_info">
                                    <span class="o_stat_text">Crédits Impactés</span>
                                </div>
                            </button>
                        </div>
                        
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="date" readonly="state in ('validated', 'cancelled')"/>
                                <field name="exercise_id" readonly="state != 'draft'"/>
                                <field name="adjustment_type" readonly="state != 'draft'"/>
                                <field name="user_id"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                                <field name="currency_id" invisible="1"/>
                            </group>
                            <group>
                                <field name="total_debit" widget="monetary"/>
                                <field name="total_credit" widget="monetary"/>
                                <field name="balance" widget="monetary" 
                                       decoration-danger="balance != 0 and adjustment_type == 'transfer'"/>
                            </group>
                        </group>
                        
                        <group string="Description">
                            <field name="description" nolabel="1" placeholder="Description de l'ajustement" 
                                   readonly="state in ('validated', 'cancelled')"/>
                        </group>
                        
                        <group string="Motif">
                            <field name="reason" nolabel="1" placeholder="Motif ou justification de l'ajustement" 
                                   readonly="state in ('validated', 'cancelled')"/>
                        </group>
                        
                        <group string="Suivi" invisible="state == 'draft'">
                            <group>
                                <field name="validated_by" readonly="1"/>
                                <field name="validated_date" readonly="1"/>
                            </group>
                        </group>
                        
                        <notebook>
                            <page string="Lignes d'Ajustement" name="lines">
                                <field name="line_ids" readonly="state in ('validated', 'cancelled')">
                                    <tree editable="bottom">
                                        <field name="sequence" widget="handle"/>
                                        <field name="nomenclature_id" 
                                               domain="[('is_analytical', '=', True), ('company_id', '=', parent.company_id)]"/>
                                        <field name="credit_id" readonly="1"/>
                                        <field name="movement_type"/>
                                        <field name="amount" widget="monetary"/>
                                        <field name="description"/>
                                        <field name="currency_id" invisible="1"/>
                                        <field name="company_id" invisible="1"/>
                                        <field name="exercise_id" invisible="1"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>
        
        <!-- Vue kanban des ajustements budgétaires -->
        <record id="view_budget_adjustment_kanban" model="ir.ui.view">
            <field name="name">budget.adjustment.kanban</field>
            <field name="model">budget.adjustment</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" default_group_by="state">
                    <field name="name"/>
                    <field name="date"/>
                    <field name="adjustment_type"/>
                    <field name="total_debit"/>
                    <field name="total_credit"/>
                    <field name="balance"/>
                    <field name="state"/>
                    <field name="currency_id"/>
                    <field name="user_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="date"/> - <field name="adjustment_type"/>
                                        </small>
                                    </div>
                                    <span class="o_kanban_record_top_right">
                                        <field name="user_id" widget="many2one_avatar_user"/>
                                    </span>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-6">
                                            <span>Débit:</span><br/>
                                            <strong><field name="total_debit" widget="monetary"/></strong>
                                        </div>
                                        <div class="col-6">
                                            <span>Crédit:</span><br/>
                                            <strong><field name="total_credit" widget="monetary"/></strong>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <span>Solde:</span><br/>
                                            <strong><field name="balance" widget="monetary"/></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        
        <!-- Vue recherche des ajustements budgétaires -->
        <record id="view_budget_adjustment_search" model="ir.ui.view">
            <field name="name">budget.adjustment.search</field>
            <field name="model">budget.adjustment</field>
            <field name="arch" type="xml">
                <search string="Rechercher Ajustements">
                    <field name="name"/>
                    <field name="exercise_id"/>
                    <field name="adjustment_type"/>
                    <field name="user_id"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <separator/>
                    <filter string="Mes ajustements" name="my_adjustments" domain="[('user_id', '=', uid)]"/>
                    <separator/>
                    <filter string="Brouillon" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="En attente" name="waiting" domain="[('state', '=', 'waiting_validation')]"/>
                    <filter string="Validé" name="validated" domain="[('state', '=', 'validated')]"/>
                    <filter string="Annulé" name="cancelled" domain="[('state', '=', 'cancelled')]"/>
                    <separator/>
                    <filter string="Virements" name="transfer" domain="[('adjustment_type', '=', 'transfer')]"/>
                    <filter string="Budgets supplémentaires" name="supplementary" domain="[('adjustment_type', '=', 'supplementary')]"/>
                    <filter string="Budgets rectificatifs" name="rectification" domain="[('adjustment_type', '=', 'rectification')]"/>
                    <filter string="Rattachements" name="carryover" domain="[('adjustment_type', '=', 'carryover')]"/>
                    <filter string="Annulations" name="cancellation" domain="[('adjustment_type', '=', 'cancellation')]"/>
                    <separator/>
                    <filter string="Non équilibrés" name="unbalanced" domain="[('balance', '!=', 0)]"/>
                    <separator/>
                    <filter string="Ce mois" name="this_month" 
                            domain="[('date', '&gt;=', context_today().strftime('%Y-%m-01')), ('date', '&lt;', (context_today() + relativedelta(months=1)).strftime('%Y-%m-01'))]"/>
                    <filter string="Exercice actuel" name="current_exercise" 
                            domain="[('exercise_id.state', '=', 'open')]"/>
                    <separator/>
                    <group expand="0" string="Grouper par">
                        <filter string="État" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Type d'ajustement" name="group_type" context="{'group_by': 'adjustment_type'}"/>
                        <filter string="Exercice" name="group_exercise" context="{'group_by': 'exercise_id'}"/>
                        <filter string="Responsable" name="group_user" context="{'group_by': 'user_id'}"/>
                        <filter string="Date" name="group_date" context="{'group_by': 'date:month'}"/>
                        <filter string="Société" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Action pour les ajustements budgétaires -->
        <record id="action_budget_adjustment" model="ir.actions.act_window">
            <field name="name">Ajustements Budgétaires</field>
            <field name="res_model">budget.adjustment</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_adjustment_search"/>
            <field name="context">{'search_default_current_exercise': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Créer un nouvel ajustement budgétaire
                </p>
                <p>
                    Les ajustements budgétaires permettent de modifier 
                    les crédits alloués en cours d'exercice.
                </p>
                <p>
                    Types d'ajustements disponibles : virements, budgets 
                    supplémentaires, rectificatifs, rattachements et annulations.
                </p>
            </field>
        </record>
        
        <!-- Actions spécifiques par type d'ajustement -->
        <record id="action_budget_adjustment_transfer" model="ir.actions.act_window">
            <field name="name">Virements de Crédits</field>
            <field name="res_model">budget.adjustment</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_adjustment_search"/>
            <field name="domain">[('adjustment_type', '=', 'transfer')]</field>
            <field name="context">{'default_adjustment_type': 'transfer', 'search_default_transfer': 1}</field>
        </record>
        
        <record id="action_budget_adjustment_supplementary" model="ir.actions.act_window">
            <field name="name">Budgets Supplémentaires</field>
            <field name="res_model">budget.adjustment</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_adjustment_search"/>
            <field name="domain">[('adjustment_type', '=', 'supplementary')]</field>
            <field name="context">{'default_adjustment_type': 'supplementary', 'search_default_supplementary': 1}</field>
        </record>
        
        <!-- Action pour les ajustements à valider -->
        <record id="action_budget_adjustment_to_validate" model="ir.actions.act_window">
            <field name="name">Ajustements à Valider</field>
            <field name="res_model">budget.adjustment</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_adjustment_search"/>
            <field name="domain">[('state', '=', 'waiting_validation')]</field>
            <field name="context">{'search_default_waiting': 1}</field>
        </record>
        
    </data>
</odoo>
