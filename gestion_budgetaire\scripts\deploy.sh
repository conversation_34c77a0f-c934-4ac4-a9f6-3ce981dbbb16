#!/bin/bash

# Script de déploiement pour le module Gestion Budgétaire
# Usage: ./deploy.sh [environment] [action]
# Environments: dev, test, prod
# Actions: install, upgrade, test

set -e  # Arrêter en cas d'erreur

# Configuration par défaut
ENVIRONMENT=${1:-dev}
ACTION=${2:-install}
MODULE_NAME="gestion_budgetaire"

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction d'affichage
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Configuration selon l'environnement
case $ENVIRONMENT in
    dev)
        DB_NAME="gestion_budgetaire_dev"
        ODOO_CONFIG="/etc/odoo/odoo-dev.conf"
        ADDONS_PATH="/opt/odoo/addons-dev"
        ;;
    test)
        DB_NAME="gestion_budgetaire_test"
        ODOO_CONFIG="/etc/odoo/odoo-test.conf"
        ADDONS_PATH="/opt/odoo/addons-test"
        ;;
    prod)
        DB_NAME="gestion_budgetaire_prod"
        ODOO_CONFIG="/etc/odoo/odoo.conf"
        ADDONS_PATH="/opt/odoo/addons"
        ;;
    *)
        error "Environnement non reconnu: $ENVIRONMENT"
        echo "Environnements disponibles: dev, test, prod"
        exit 1
        ;;
esac

log "Déploiement du module $MODULE_NAME en environnement $ENVIRONMENT"

# Vérifications préalables
check_prerequisites() {
    log "Vérification des prérequis..."
    
    # Vérifier que le répertoire addons existe
    if [ ! -d "$ADDONS_PATH" ]; then
        error "Répertoire addons non trouvé: $ADDONS_PATH"
        exit 1
    fi
    
    # Vérifier que le fichier de configuration existe
    if [ ! -f "$ODOO_CONFIG" ]; then
        error "Fichier de configuration non trouvé: $ODOO_CONFIG"
        exit 1
    fi
    
    # Vérifier que PostgreSQL est accessible
    if ! pg_isready -q; then
        error "PostgreSQL n'est pas accessible"
        exit 1
    fi
    
    success "Prérequis vérifiés"
}

# Copier le module
copy_module() {
    log "Copie du module vers $ADDONS_PATH..."
    
    # Créer une sauvegarde si le module existe déjà
    if [ -d "$ADDONS_PATH/$MODULE_NAME" ]; then
        warning "Module existant trouvé, création d'une sauvegarde..."
        cp -r "$ADDONS_PATH/$MODULE_NAME" "$ADDONS_PATH/${MODULE_NAME}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # Copier le nouveau module
    cp -r "$(dirname "$0")/../" "$ADDONS_PATH/$MODULE_NAME"
    
    # Supprimer les fichiers non nécessaires
    rm -rf "$ADDONS_PATH/$MODULE_NAME/.git"
    rm -rf "$ADDONS_PATH/$MODULE_NAME/.vscode"
    rm -rf "$ADDONS_PATH/$MODULE_NAME/scripts"
    rm -f "$ADDONS_PATH/$MODULE_NAME/.gitignore"
    rm -f "$ADDONS_PATH/$MODULE_NAME/.odoo_test"
    
    success "Module copié"
}

# Installer le module
install_module() {
    log "Installation du module $MODULE_NAME..."
    
    odoo-bin -c "$ODOO_CONFIG" -d "$DB_NAME" -i "$MODULE_NAME" --stop-after-init
    
    if [ $? -eq 0 ]; then
        success "Module installé avec succès"
    else
        error "Échec de l'installation du module"
        exit 1
    fi
}

# Mettre à jour le module
upgrade_module() {
    log "Mise à jour du module $MODULE_NAME..."
    
    odoo-bin -c "$ODOO_CONFIG" -d "$DB_NAME" -u "$MODULE_NAME" --stop-after-init
    
    if [ $? -eq 0 ]; then
        success "Module mis à jour avec succès"
    else
        error "Échec de la mise à jour du module"
        exit 1
    fi
}

# Exécuter les tests
run_tests() {
    log "Exécution des tests pour $MODULE_NAME..."
    
    # Créer une base de données de test
    TEST_DB="${DB_NAME}_test"
    
    # Supprimer la base de test si elle existe
    dropdb --if-exists "$TEST_DB"
    
    # Créer la base de test
    createdb "$TEST_DB"
    
    # Exécuter les tests
    odoo-bin -c "$ODOO_CONFIG" -d "$TEST_DB" -i "$MODULE_NAME" --test-enable --stop-after-init
    
    if [ $? -eq 0 ]; then
        success "Tests exécutés avec succès"
    else
        error "Échec des tests"
        exit 1
    fi
    
    # Nettoyer la base de test
    dropdb "$TEST_DB"
}

# Redémarrer Odoo
restart_odoo() {
    log "Redémarrage d'Odoo..."
    
    if systemctl is-active --quiet odoo; then
        systemctl restart odoo
        success "Odoo redémarré"
    else
        warning "Service Odoo non actif"
    fi
}

# Fonction principale
main() {
    log "Début du déploiement - Environnement: $ENVIRONMENT, Action: $ACTION"
    
    check_prerequisites
    copy_module
    
    case $ACTION in
        install)
            install_module
            ;;
        upgrade)
            upgrade_module
            ;;
        test)
            run_tests
            ;;
        *)
            error "Action non reconnue: $ACTION"
            echo "Actions disponibles: install, upgrade, test"
            exit 1
            ;;
    esac
    
    if [ "$ENVIRONMENT" != "test" ]; then
        restart_odoo
    fi
    
    success "Déploiement terminé avec succès!"
}

# Exécution du script principal
main
