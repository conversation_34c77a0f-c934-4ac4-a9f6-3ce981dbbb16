# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime
import logging

_logger = logging.getLogger(__name__)


class BudgetEngagement(models.Model):
    """Modèle pour la gestion des engagements budgétaires"""
    
    _name = 'budget.engagement'
    _description = 'Engagement Budgétaire'
    _order = 'date desc, name desc'
    _check_company_auto = True
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    # Champs de base
    name = fields.Char(
        string='Référence',
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: _('Nouveau'),
        help='Référence unique de l\'engagement'
    )
    
    date = fields.Date(
        string='Date d\'engagement',
        required=True,
        default=fields.Date.context_today,
        tracking=True,
        help='Date de création de l\'engagement'
    )
    
    exercise_id = fields.Many2one(
        'budget.exercise',
        string='Exercice budgétaire',
        required=True,
        domain="[('state', '=', 'open'), ('company_id', '=', company_id)]",
        help='Exercice budgétaire concerné'
    )
    
    nomenclature_id = fields.Many2one(
        'budget.nomenclature',
        string='Poste budgétaire',
        required=True,
        domain="[('is_analytical', '=', True), ('company_id', '=', company_id)]",
        help='Poste budgétaire impacté'
    )
    
    credit_id = fields.Many2one(
        'budget.credit',
        string='Crédit budgétaire',
        compute='_compute_credit_id',
        store=True,
        help='Crédit budgétaire correspondant'
    )
    
    engagement_type_id = fields.Many2one(
        'budget.engagement.type',
        string='Type d\'engagement',
        required=True,
        domain="[('company_id', '=', company_id)]",
        help='Type d\'engagement budgétaire'
    )
    
    # Montant et devise
    amount = fields.Monetary(
        string='Montant',
        currency_field='currency_id',
        required=True,
        tracking=True,
        help='Montant de l\'engagement'
    )
    
    amount_mandated = fields.Monetary(
        string='Montant mandaté',
        compute='_compute_amounts',
        store=True,
        currency_field='currency_id',
        help='Montant déjà mandaté'
    )
    
    amount_remaining = fields.Monetary(
        string='Reste à mandater',
        compute='_compute_amounts',
        store=True,
        currency_field='currency_id',
        help='Montant restant à mandater'
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Société',
        required=True,
        default=lambda self: self.env.company,
        help='Société concernée'
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Devise',
        related='company_id.currency_id',
        store=True,
        readonly=True
    )
    
    # Tiers
    partner_id = fields.Many2one(
        'res.partner',
        string='Tiers',
        help='Fournisseur ou bénéficiaire de l\'engagement'
    )
    
    # État et workflow
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('waiting_validation', 'En attente de validation'),
        ('validated', 'Validé'),
        ('partially_mandated', 'Partiellement mandaté'),
        ('fully_mandated', 'Totalement mandaté'),
        ('cancelled', 'Annulé'),
    ], string='État', default='draft', required=True, tracking=True,
       help='État de l\'engagement')
    
    # Documents sources
    purchase_order_id = fields.Many2one(
        'purchase.order',
        string='Commande d\'achat',
        help='Commande d\'achat source'
    )
    
    expense_id = fields.Many2one(
        'hr.expense',
        string='Note de frais',
        help='Note de frais source'
    )
    
    invoice_id = fields.Many2one(
        'account.move',
        string='Facture',
        help='Facture liée'
    )
    
    # Description et motif
    description = fields.Text(
        string='Description',
        required=True,
        help='Description de l\'engagement'
    )
    
    reason = fields.Text(
        string='Motif',
        help='Motif ou justification de l\'engagement'
    )
    
    # Relations
    mandate_ids = fields.One2many(
        'budget.mandate',
        'engagement_id',
        string='Mandats',
        help='Mandats liés à cet engagement'
    )
    
    # Champs de suivi
    user_id = fields.Many2one(
        'res.users',
        string='Responsable',
        default=lambda self: self.env.user,
        help='Utilisateur responsable de l\'engagement'
    )
    
    validated_by = fields.Many2one(
        'res.users',
        string='Validé par',
        readonly=True,
        help='Utilisateur ayant validé l\'engagement'
    )
    
    validated_date = fields.Datetime(
        string='Date de validation',
        readonly=True,
        help='Date et heure de validation'
    )
    
    # Contraintes
    @api.constrains('amount', 'engagement_type_id')
    def _check_amount(self):
        """Vérifier la cohérence du montant selon le type"""
        for record in self:
            if not record.engagement_type_id.allow_negative_amount and record.amount < 0:
                raise ValidationError(
                    _('Le montant ne peut pas être négatif pour ce type d\'engagement.')
                )
    
    @api.constrains('date', 'exercise_id')
    def _check_date_in_exercise(self):
        """Vérifier que la date est dans l'exercice"""
        for record in self:
            if not (record.exercise_id.date_start <= record.date <= record.exercise_id.date_end):
                raise ValidationError(
                    _('La date d\'engagement doit être comprise dans l\'exercice budgétaire.')
                )
    
    # Méthodes de calcul
    @api.depends('exercise_id', 'nomenclature_id')
    def _compute_credit_id(self):
        """Calculer le crédit budgétaire correspondant"""
        for record in self:
            if record.exercise_id and record.nomenclature_id:
                credit = self.env['budget.credit'].search([
                    ('exercise_id', '=', record.exercise_id.id),
                    ('nomenclature_id', '=', record.nomenclature_id.id)
                ], limit=1)
                record.credit_id = credit
            else:
                record.credit_id = False
    
    @api.depends('mandate_ids.amount', 'mandate_ids.state')
    def _compute_amounts(self):
        """Calculer les montants mandatés"""
        for record in self:
            mandated_amount = sum(
                mandate.amount for mandate in record.mandate_ids
                if mandate.state == 'paid'
            )
            record.amount_mandated = mandated_amount
            record.amount_remaining = record.amount - mandated_amount
    
    # Méthodes CRUD
    @api.model_create_multi
    def create(self, vals_list):
        """Créer un engagement avec numérotation automatique"""
        for vals in vals_list:
            if vals.get('name', _('Nouveau')) == _('Nouveau'):
                vals['name'] = self.env['ir.sequence'].next_by_code('budget.engagement') or _('Nouveau')
        
        engagements = super().create(vals_list)
        
        # Vérifier la disponibilité budgétaire pour les nouveaux engagements
        for engagement in engagements:
            if engagement.state == 'validated':
                engagement._check_budget_availability()
        
        return engagements
    
    def write(self, vals):
        """Mise à jour avec contrôles budgétaires"""
        # Si changement d'état vers validé, vérifier la disponibilité
        if vals.get('state') == 'validated':
            for record in self:
                record._check_budget_availability()
        
        return super().write(vals)
    
    # Méthodes de contrôle budgétaire
    def _check_budget_availability(self):
        """Vérifier la disponibilité budgétaire"""
        self.ensure_one()
        
        if not self.credit_id:
            raise UserError(
                _('Aucun crédit budgétaire trouvé pour le poste %s sur l\'exercice %s') %
                (self.nomenclature_id.name, self.exercise_id.name)
            )
        
        # Calculer l'impact selon le type d'engagement
        impact_amount = self.engagement_type_id.get_impact_amount(self.amount)
        
        if impact_amount > 0:  # Consommation
            self.credit_id.check_availability(impact_amount)
    
    # Actions de workflow
    def action_submit_for_validation(self):
        """Soumettre pour validation"""
        for record in self:
            if record.state != 'draft':
                raise UserError(_('Seul un engagement en brouillon peut être soumis.'))
            record.state = 'waiting_validation'
    
    def action_validate(self):
        """Valider l'engagement"""
        for record in self:
            if record.state != 'waiting_validation':
                raise UserError(_('Seul un engagement en attente peut être validé.'))
            
            # Vérifier la disponibilité budgétaire
            record._check_budget_availability()
            
            record.write({
                'state': 'validated',
                'validated_by': self.env.user.id,
                'validated_date': datetime.now(),
            })
            
            # Créer automatiquement un mandat si configuré
            if record.engagement_type_id.auto_create_mandate:
                record._create_automatic_mandate()
            
            _logger.info('Engagement %s validé par %s', record.name, self.env.user.name)
    
    def action_cancel(self):
        """Annuler l'engagement"""
        for record in self:
            if record.state in ('fully_mandated', 'cancelled'):
                raise UserError(_('Cet engagement ne peut pas être annulé.'))
            
            if record.mandate_ids.filtered(lambda m: m.state == 'paid'):
                raise UserError(_('Impossible d\'annuler un engagement avec des mandats payés.'))
            
            # Annuler les mandats en attente
            record.mandate_ids.filtered(lambda m: m.state != 'paid').action_cancel()
            
            record.state = 'cancelled'
            _logger.info('Engagement %s annulé', record.name)
    
    def action_reset_to_draft(self):
        """Remettre en brouillon"""
        for record in self:
            if record.state not in ('waiting_validation', 'cancelled'):
                raise UserError(_('Seul un engagement en attente ou annulé peut être remis en brouillon.'))
            record.state = 'draft'
    
    def _create_automatic_mandate(self):
        """Créer automatiquement un mandat"""
        self.ensure_one()
        
        mandate_vals = {
            'engagement_id': self.id,
            'amount': self.amount,
            'partner_id': self.partner_id.id,
            'description': f"Mandat automatique - {self.description}",
        }
        
        self.env['budget.mandate'].create(mandate_vals)
    
    # Actions de vue
    def action_view_mandates(self):
        """Voir les mandats de cet engagement"""
        self.ensure_one()
        return {
            'name': _('Mandats'),
            'type': 'ir.actions.act_window',
            'res_model': 'budget.mandate',
            'view_mode': 'tree,form',
            'domain': [('engagement_id', '=', self.id)],
            'context': {'default_engagement_id': self.id},
        }
    
    def action_create_mandate(self):
        """Créer un nouveau mandat"""
        self.ensure_one()
        
        if self.state != 'validated':
            raise UserError(_('Seul un engagement validé peut générer un mandat.'))
        
        if self.amount_remaining <= 0:
            raise UserError(_('Cet engagement est déjà totalement mandaté.'))
        
        return {
            'name': _('Créer un mandat'),
            'type': 'ir.actions.act_window',
            'res_model': 'budget.mandate',
            'view_mode': 'form',
            'context': {
                'default_engagement_id': self.id,
                'default_amount': self.amount_remaining,
                'default_partner_id': self.partner_id.id,
                'default_description': f"Mandat pour {self.description}",
            },
            'target': 'new',
        }
    
    # Méthodes utilitaires
    @api.onchange('exercise_id', 'nomenclature_id')
    def _onchange_credit_computation(self):
        """Recalculer le crédit lors du changement d'exercice ou de poste"""
        if self.exercise_id and self.nomenclature_id:
            credit = self.env['budget.credit'].search([
                ('exercise_id', '=', self.exercise_id.id),
                ('nomenclature_id', '=', self.nomenclature_id.id)
            ], limit=1)
            if not credit:
                return {
                    'warning': {
                        'title': _('Attention'),
                        'message': _('Aucun crédit budgétaire trouvé pour ce poste sur cet exercice.')
                    }
                }
    
    @api.onchange('amount_remaining')
    def _onchange_state_from_mandates(self):
        """Mettre à jour l'état selon les mandatements"""
        for record in self:
            if record.state == 'validated':
                if record.amount_mandated == 0:
                    continue  # Reste validé
                elif record.amount_remaining > 0:
                    record.state = 'partially_mandated'
                else:
                    record.state = 'fully_mandated'
