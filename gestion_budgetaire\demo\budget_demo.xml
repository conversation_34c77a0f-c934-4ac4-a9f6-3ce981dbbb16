<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Exercice budgétaire de démonstration -->
        <record id="demo_exercise_2024" model="budget.exercise">
            <field name="name">Exercice Budgétaire 2024</field>
            <field name="code">2024</field>
            <field name="date_start">2024-01-01</field>
            <field name="date_end">2024-12-31</field>
            <field name="state">open</field>
            <field name="description">Exercice budgétaire de démonstration pour l'année 2024</field>
        </record>
        
        <!-- Crédits budgétaires de démonstration -->
        <record id="demo_credit_salaires" model="budget.credit">
            <field name="exercise_id" ref="demo_exercise_2024"/>
            <field name="nomenclature_id" ref="nomenclature_article_salaires"/>
            <field name="amount_initial">5000000.00</field>
            <field name="state">validated</field>
            <field name="alert_threshold">80.0</field>
            <field name="blocking_threshold">100.0</field>
        </record>
        
        <record id="demo_credit_charges_sociales" model="budget.credit">
            <field name="exercise_id" ref="demo_exercise_2024"/>
            <field name="nomenclature_id" ref="nomenclature_article_charges_sociales"/>
            <field name="amount_initial">1500000.00</field>
            <field name="state">validated</field>
            <field name="alert_threshold">80.0</field>
            <field name="blocking_threshold">100.0</field>
        </record>
        
        <record id="demo_credit_fournitures" model="budget.credit">
            <field name="exercise_id" ref="demo_exercise_2024"/>
            <field name="nomenclature_id" ref="nomenclature_article_fournitures"/>
            <field name="amount_initial">800000.00</field>
            <field name="state">validated</field>
            <field name="alert_threshold">80.0</field>
            <field name="blocking_threshold">100.0</field>
        </record>
        
        <record id="demo_credit_services" model="budget.credit">
            <field name="exercise_id" ref="demo_exercise_2024"/>
            <field name="nomenclature_id" ref="nomenclature_article_services"/>
            <field name="amount_initial">1200000.00</field>
            <field name="state">validated</field>
            <field name="alert_threshold">80.0</field>
            <field name="blocking_threshold">100.0</field>
        </record>
        
        <record id="demo_credit_informatique" model="budget.credit">
            <field name="exercise_id" ref="demo_exercise_2024"/>
            <field name="nomenclature_id" ref="nomenclature_article_informatique"/>
            <field name="amount_initial">2000000.00</field>
            <field name="state">validated</field>
            <field name="alert_threshold">80.0</field>
            <field name="blocking_threshold">100.0</field>
        </record>
        
        <record id="demo_credit_subvention_etat" model="budget.credit">
            <field name="exercise_id" ref="demo_exercise_2024"/>
            <field name="nomenclature_id" ref="nomenclature_article_subvention_etat"/>
            <field name="amount_initial">10000000.00</field>
            <field name="state">validated</field>
            <field name="alert_threshold">80.0</field>
            <field name="blocking_threshold">100.0</field>
        </record>
        
        <!-- Partenaires de démonstration -->
        <record id="demo_partner_fournisseur_1" model="res.partner">
            <field name="name">Fournisseur Bureautique SARL</field>
            <field name="is_company">True</field>
            <field name="supplier_rank">1</field>
            <field name="street">Zone Industrielle</field>
            <field name="city">Alger</field>
            <field name="country_id" ref="base.dz"/>
            <field name="phone">+213 21 123 456</field>
            <field name="email"><EMAIL></field>
        </record>
        
        <record id="demo_partner_fournisseur_2" model="res.partner">
            <field name="name">Informatique Solutions EURL</field>
            <field name="is_company">True</field>
            <field name="supplier_rank">1</field>
            <field name="street">Cité des Programmeurs</field>
            <field name="city">Oran</field>
            <field name="country_id" ref="base.dz"/>
            <field name="phone">+213 41 987 654</field>
            <field name="email"><EMAIL></field>
        </record>
        
        <record id="demo_partner_client_1" model="res.partner">
            <field name="name">Ministère de l'Éducation</field>
            <field name="is_company">True</field>
            <field name="customer_rank">1</field>
            <field name="street">Avenue de l'Indépendance</field>
            <field name="city">Alger</field>
            <field name="country_id" ref="base.dz"/>
            <field name="phone">+213 21 555 000</field>
            <field name="email"><EMAIL></field>
        </record>
        
        <!-- Engagements de démonstration -->
        <record id="demo_engagement_1" model="budget.engagement">
            <field name="exercise_id" ref="demo_exercise_2024"/>
            <field name="nomenclature_id" ref="nomenclature_article_fournitures"/>
            <field name="engagement_type_id" ref="engagement_type_pec"/>
            <field name="partner_id" ref="demo_partner_fournisseur_1"/>
            <field name="amount">150000.00</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="description">Achat de fournitures de bureau pour le premier trimestre</field>
            <field name="reason">Renouvellement du stock de fournitures</field>
            <field name="state">validated</field>
        </record>
        
        <record id="demo_engagement_2" model="budget.engagement">
            <field name="exercise_id" ref="demo_exercise_2024"/>
            <field name="nomenclature_id" ref="nomenclature_article_informatique"/>
            <field name="engagement_type_id" ref="engagement_type_pec"/>
            <field name="partner_id" ref="demo_partner_fournisseur_2"/>
            <field name="amount">800000.00</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
            <field name="description">Acquisition d'équipements informatiques</field>
            <field name="reason">Modernisation du parc informatique</field>
            <field name="state">validated</field>
        </record>
        
        <!-- Mandats de démonstration -->
        <record id="demo_mandate_1" model="budget.mandate">
            <field name="engagement_id" ref="demo_engagement_1"/>
            <field name="partner_id" ref="demo_partner_fournisseur_1"/>
            <field name="payment_type_id" ref="payment_type_banque"/>
            <field name="amount">150000.00</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=20)).strftime('%Y-%m-%d')"/>
            <field name="description">Paiement fournitures de bureau</field>
            <field name="state">paid</field>
            <field name="payment_date" eval="(DateTime.now() - timedelta(days=15)).strftime('%Y-%m-%d')"/>
        </record>
        
        <!-- Titre de perception de démonstration -->
        <record id="demo_perception_title_1" model="budget.perception.title">
            <field name="exercise_id" ref="demo_exercise_2024"/>
            <field name="nomenclature_id" ref="nomenclature_article_subvention_etat"/>
            <field name="partner_id" ref="demo_partner_client_1"/>
            <field name="amount">2000000.00</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=60)).strftime('%Y-%m-%d')"/>
            <field name="due_date" eval="(DateTime.now() + timedelta(days=30)).strftime('%Y-%m-%d')"/>
            <field name="description">Subvention annuelle de l'État - Premier trimestre</field>
            <field name="reason">Financement des activités du premier trimestre</field>
            <field name="state">issued</field>
        </record>
        
        <!-- Encaissement de démonstration -->
        <record id="demo_collection_1" model="budget.perception.collection">
            <field name="title_id" ref="demo_perception_title_1"/>
            <field name="amount">1000000.00</field>
            <field name="date" eval="(DateTime.now() - timedelta(days=45)).strftime('%Y-%m-%d')"/>
            <field name="description">Encaissement partiel subvention État</field>
            <field name="state">collected</field>
        </record>
        
    </data>
</odoo>
