# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError
from datetime import date, timedelta


class TestBudgetExercise(TransactionCase):
    """Tests pour le modèle budget.exercise"""

    def setUp(self):
        super().setUp()
        self.BudgetExercise = self.env['budget.exercise']
        self.company = self.env.ref('base.main_company')

    def test_create_exercise(self):
        """Test de création d'un exercice budgétaire"""
        exercise = self.BudgetExercise.create({
            'name': 'Test Exercise 2024',
            'code': 'TEST2024',
            'date_start': date(2024, 1, 1),
            'date_end': date(2024, 12, 31),
            'company_id': self.company.id,
        })
        
        self.assertEqual(exercise.state, 'draft')
        self.assertEqual(exercise.name, 'Test Exercise 2024')
        self.assertEqual(exercise.code, 'TEST2024')

    def test_exercise_date_validation(self):
        """Test de validation des dates d'exercice"""
        # Test date de fin antérieure à la date de début
        with self.assertRaises(ValidationError):
            self.BudgetExercise.create({
                'name': 'Invalid Exercise',
                'code': 'INVALID',
                'date_start': date(2024, 12, 31),
                'date_end': date(2024, 1, 1),
                'company_id': self.company.id,
            })

    def test_exercise_code_uniqueness(self):
        """Test d'unicité du code d'exercice par société"""
        # Créer le premier exercice
        self.BudgetExercise.create({
            'name': 'Exercise 1',
            'code': 'UNIQUE2024',
            'date_start': date(2024, 1, 1),
            'date_end': date(2024, 12, 31),
            'company_id': self.company.id,
        })
        
        # Tenter de créer un second exercice avec le même code
        with self.assertRaises(ValidationError):
            self.BudgetExercise.create({
                'name': 'Exercise 2',
                'code': 'UNIQUE2024',
                'date_start': date(2025, 1, 1),
                'date_end': date(2025, 12, 31),
                'company_id': self.company.id,
            })

    def test_exercise_workflow(self):
        """Test du workflow d'un exercice"""
        exercise = self.BudgetExercise.create({
            'name': 'Workflow Test',
            'code': 'WORKFLOW2024',
            'date_start': date(2024, 1, 1),
            'date_end': date(2024, 12, 31),
            'company_id': self.company.id,
        })
        
        # Test ouverture
        exercise.action_open()
        self.assertEqual(exercise.state, 'open')
        
        # Test mise en clôture
        exercise.action_set_closing()
        self.assertEqual(exercise.state, 'closing')
        
        # Test fermeture
        exercise.action_close()
        self.assertEqual(exercise.state, 'closed')

    def test_get_current_exercise(self):
        """Test de récupération de l'exercice actuel"""
        # Créer un exercice pour l'année courante
        current_year = date.today().year
        exercise = self.BudgetExercise.create({
            'name': f'Current Exercise {current_year}',
            'code': f'CURRENT{current_year}',
            'date_start': date(current_year, 1, 1),
            'date_end': date(current_year, 12, 31),
            'company_id': self.company.id,
            'state': 'open',
        })
        
        # Récupérer l'exercice actuel
        current_exercise = self.BudgetExercise.get_current_exercise(self.company.id)
        self.assertEqual(current_exercise, exercise)

    def test_exercise_overlapping_dates(self):
        """Test de détection des chevauchements de dates"""
        # Créer le premier exercice
        self.BudgetExercise.create({
            'name': 'Exercise 1',
            'code': 'EX1',
            'date_start': date(2024, 1, 1),
            'date_end': date(2024, 12, 31),
            'company_id': self.company.id,
        })
        
        # Tenter de créer un exercice avec des dates qui se chevauchent
        with self.assertRaises(ValidationError):
            self.BudgetExercise.create({
                'name': 'Exercise 2',
                'code': 'EX2',
                'date_start': date(2024, 6, 1),
                'date_end': date(2025, 5, 31),
                'company_id': self.company.id,
            })

    def test_exercise_totals_computation(self):
        """Test du calcul des totaux d'exercice"""
        exercise = self.BudgetExercise.create({
            'name': 'Totals Test',
            'code': 'TOTALS2024',
            'date_start': date(2024, 1, 1),
            'date_end': date(2024, 12, 31),
            'company_id': self.company.id,
        })
        
        # Les totaux doivent être à zéro initialement
        self.assertEqual(exercise.total_credits_initial, 0)
        self.assertEqual(exercise.total_credits_voted, 0)
        self.assertEqual(exercise.total_engagements, 0)
        self.assertEqual(exercise.total_available_credits, 0)
