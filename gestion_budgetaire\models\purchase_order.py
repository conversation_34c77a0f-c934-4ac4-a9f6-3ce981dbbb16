# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
import logging

_logger = logging.getLogger(__name__)


class PurchaseOrder(models.Model):
    """Extension du modèle purchase.order pour l'intégration budgétaire"""
    
    _inherit = 'purchase.order'
    
    # Champs budgétaires
    budget_engagement_ids = fields.One2many(
        'budget.engagement',
        'purchase_order_id',
        string='Engagements budgétaires',
        help='Engagements budgétaires générés par cette commande'
    )
    
    budget_total_engaged = fields.Monetary(
        string='Total engagé',
        compute='_compute_budget_amounts',
        store=True,
        help='Montant total engagé sur le budget'
    )
    
    budget_control_required = fields.Boolean(
        string='Contrôle budgétaire requis',
        compute='_compute_budget_control_required',
        store=True,
        help='Indique si cette commande nécessite un contrôle budgétaire'
    )
    
    budget_validated = fields.Boolean(
        string='Budget validé',
        compute='_compute_budget_validated',
        store=True,
        help='Indique si le budget est validé pour cette commande'
    )
    
    # Méthodes de calcul
    @api.depends('budget_engagement_ids.amount', 'budget_engagement_ids.state')
    def _compute_budget_amounts(self):
        """Calculer les montants budgétaires"""
        for order in self:
            order.budget_total_engaged = sum(
                engagement.amount for engagement in order.budget_engagement_ids
                if engagement.state == 'validated'
            )
    
    @api.depends('order_line.budget_nomenclature_id')
    def _compute_budget_control_required(self):
        """Déterminer si un contrôle budgétaire est requis"""
        for order in self:
            order.budget_control_required = any(
                line.budget_nomenclature_id for line in order.order_line
            )
    
    @api.depends('budget_engagement_ids.state')
    def _compute_budget_validated(self):
        """Déterminer si le budget est validé"""
        for order in self:
            if order.budget_control_required:
                order.budget_validated = all(
                    engagement.state == 'validated' 
                    for engagement in order.budget_engagement_ids
                )
            else:
                order.budget_validated = True
    
    # Surcharge des méthodes
    def button_confirm(self):
        """Surcharge pour inclure le contrôle budgétaire"""
        # Vérifier et créer les engagements budgétaires
        for order in self:
            if order.budget_control_required:
                order._create_budget_engagements()
        
        return super().button_confirm()
    
    def button_cancel(self):
        """Surcharge pour annuler les engagements budgétaires"""
        # Annuler les engagements budgétaires
        for order in self:
            order.budget_engagement_ids.filtered(
                lambda e: e.state not in ('fully_mandated', 'cancelled')
            ).action_cancel()
        
        return super().button_cancel()
    
    # Méthodes budgétaires
    def _create_budget_engagements(self):
        """Créer les engagements budgétaires pour cette commande"""
        self.ensure_one()
        
        if not self.budget_control_required:
            return
        
        # Obtenir l'exercice budgétaire actuel
        current_exercise = self.env['budget.exercise'].get_current_exercise(self.company_id.id)
        if not current_exercise:
            raise UserError(
                _('Aucun exercice budgétaire ouvert trouvé pour la société %s') % self.company_id.name
            )
        
        # Grouper les lignes par poste budgétaire
        budget_lines = {}
        for line in self.order_line.filtered('budget_nomenclature_id'):
            nomenclature = line.budget_nomenclature_id
            if nomenclature not in budget_lines:
                budget_lines[nomenclature] = {
                    'amount': 0.0,
                    'lines': []
                }
            budget_lines[nomenclature]['amount'] += line.price_subtotal
            budget_lines[nomenclature]['lines'].append(line)
        
        # Créer les engagements
        engagement_type = self._get_default_engagement_type()
        
        for nomenclature, data in budget_lines.items():
            engagement_vals = {
                'exercise_id': current_exercise.id,
                'nomenclature_id': nomenclature.id,
                'engagement_type_id': engagement_type.id,
                'amount': data['amount'],
                'partner_id': self.partner_id.id,
                'purchase_order_id': self.id,
                'description': f"Engagement pour commande {self.name}",
                'reason': f"Commande d'achat {self.name} - {len(data['lines'])} ligne(s)",
                'state': 'waiting_validation',
            }
            
            engagement = self.env['budget.engagement'].create(engagement_vals)
            
            # Auto-valider si configuré
            if engagement_type.requires_validation:
                try:
                    engagement.action_validate()
                except UserError as e:
                    # Si la validation échoue, annuler la commande
                    raise UserError(
                        _('Impossible de valider l\'engagement budgétaire pour %s: %s') %
                        (nomenclature.name, str(e))
                    )
    
    def _get_default_engagement_type(self):
        """Obtenir le type d'engagement par défaut pour les achats"""
        engagement_type = self.env['budget.engagement.type'].search([
            ('code', '=', 'PEC'),
            ('company_id', '=', self.company_id.id)
        ], limit=1)
        
        if not engagement_type:
            raise UserError(
                _('Aucun type d\'engagement "Prise en charge" (PEC) configuré pour la société %s') %
                self.company_id.name
            )
        
        return engagement_type
    
    # Actions
    def action_view_budget_engagements(self):
        """Voir les engagements budgétaires de cette commande"""
        self.ensure_one()
        return {
            'name': _('Engagements budgétaires'),
            'type': 'ir.actions.act_window',
            'res_model': 'budget.engagement',
            'view_mode': 'tree,form',
            'domain': [('purchase_order_id', '=', self.id)],
            'context': {'default_purchase_order_id': self.id},
        }


class PurchaseOrderLine(models.Model):
    """Extension du modèle purchase.order.line pour l'imputation budgétaire"""
    
    _inherit = 'purchase.order.line'
    
    # Champ d'imputation budgétaire
    budget_nomenclature_id = fields.Many2one(
        'budget.nomenclature',
        string='Poste budgétaire',
        domain="[('is_analytical', '=', True), ('budget_type', '=', 'expense'), ('company_id', '=', company_id)]",
        help='Poste budgétaire à imputer'
    )
    
    # Champs informatifs
    budget_available = fields.Monetary(
        string='Crédit disponible',
        compute='_compute_budget_info',
        help='Crédit disponible sur le poste budgétaire'
    )
    
    budget_consumption_rate = fields.Float(
        string='Taux de consommation (%)',
        compute='_compute_budget_info',
        help='Taux de consommation du poste budgétaire'
    )
    
    company_id = fields.Many2one(
        'res.company',
        related='order_id.company_id',
        store=True,
        readonly=True
    )
    
    # Méthodes de calcul
    @api.depends('budget_nomenclature_id', 'price_subtotal')
    def _compute_budget_info(self):
        """Calculer les informations budgétaires"""
        for line in self:
            if line.budget_nomenclature_id and line.order_id.company_id:
                # Obtenir l'exercice actuel
                current_exercise = self.env['budget.exercise'].get_current_exercise(
                    line.order_id.company_id.id
                )
                
                if current_exercise:
                    # Chercher le crédit budgétaire
                    credit = self.env['budget.credit'].search([
                        ('exercise_id', '=', current_exercise.id),
                        ('nomenclature_id', '=', line.budget_nomenclature_id.id)
                    ], limit=1)
                    
                    if credit:
                        line.budget_available = credit.amount_available
                        line.budget_consumption_rate = credit.consumption_rate
                    else:
                        line.budget_available = 0.0
                        line.budget_consumption_rate = 0.0
                else:
                    line.budget_available = 0.0
                    line.budget_consumption_rate = 0.0
            else:
                line.budget_available = 0.0
                line.budget_consumption_rate = 0.0
    
    # Contraintes et validations
    @api.constrains('budget_nomenclature_id', 'price_subtotal')
    def _check_budget_availability(self):
        """Vérifier la disponibilité budgétaire (optionnel selon configuration)"""
        # Cette vérification peut être activée/désactivée selon la configuration
        budget_check_enabled = self.env['ir.config_parameter'].sudo().get_param(
            'gestion_budgetaire.check_budget_on_purchase_line', default=False
        )
        
        if not budget_check_enabled:
            return
        
        for line in self:
            if line.budget_nomenclature_id and line.price_subtotal > 0:
                if line.budget_available < line.price_subtotal:
                    raise ValidationError(
                        _('Crédit insuffisant sur le poste %s.\n'
                          'Disponible: %s, Demandé: %s') %
                        (line.budget_nomenclature_id.name, 
                         line.budget_available, 
                         line.price_subtotal)
                    )
    
    # Méthodes utilitaires
    @api.onchange('product_id')
    def _onchange_product_budget_nomenclature(self):
        """Proposer automatiquement un poste budgétaire selon le produit"""
        if self.product_id and self.product_id.budget_nomenclature_id:
            self.budget_nomenclature_id = self.product_id.budget_nomenclature_id
    
    @api.onchange('budget_nomenclature_id')
    def _onchange_budget_nomenclature_warning(self):
        """Afficher un avertissement si le crédit est insuffisant"""
        if self.budget_nomenclature_id and self.price_subtotal > 0:
            if self.budget_available < self.price_subtotal:
                return {
                    'warning': {
                        'title': _('Attention - Crédit insuffisant'),
                        'message': _('Le crédit disponible (%s) est insuffisant pour cette ligne (%s).\n'
                                   'Poste: %s\nTaux de consommation actuel: %.1f%%') %
                                 (self.budget_available, 
                                  self.price_subtotal,
                                  self.budget_nomenclature_id.name,
                                  self.budget_consumption_rate)
                    }
                }
            elif self.budget_consumption_rate > 80:
                return {
                    'warning': {
                        'title': _('Attention - Seuil d\'alerte'),
                        'message': _('Le taux de consommation du poste %s est de %.1f%%.\n'
                                   'Crédit disponible: %s') %
                                 (self.budget_nomenclature_id.name,
                                  self.budget_consumption_rate,
                                  self.budget_available)
                    }
                }
