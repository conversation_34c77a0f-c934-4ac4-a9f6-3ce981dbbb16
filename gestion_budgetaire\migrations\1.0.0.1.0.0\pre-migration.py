# -*- coding: utf-8 -*-

import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)


def migrate(cr, version):
    """Migration script for gestion_budgetaire module"""
    
    _logger.info('Starting migration of gestion_budgetaire from %s to 1.0.0', version)
    
    env = api.Environment(cr, SUPERUSER_ID, {})
    
    # Exemple de migration : mise à jour des séquences
    _update_sequences(env)
    
    # Exemple de migration : mise à jour des données existantes
    _update_existing_data(env)
    
    _logger.info('Migration of gestion_budgetaire completed successfully')


def _update_sequences(env):
    """Mettre à jour les séquences si nécessaire"""
    try:
        # Vérifier si les séquences existent
        sequences_to_check = [
            'budget.engagement',
            'budget.mandate',
            'budget.perception.title',
            'budget.perception.collection',
        ]
        
        for seq_code in sequences_to_check:
            sequence = env['ir.sequence'].search([('code', '=', seq_code)], limit=1)
            if not sequence:
                _logger.warning('Sequence %s not found, it will be created by data files', seq_code)
            else:
                _logger.info('Sequence %s found and valid', seq_code)
                
    except Exception as e:
        _logger.error('Error updating sequences: %s', str(e))


def _update_existing_data(env):
    """Mettre à jour les données existantes si nécessaire"""
    try:
        # Exemple : mettre à jour les états des exercices
        exercises = env['budget.exercise'].search([])
        for exercise in exercises:
            if not exercise.state:
                exercise.state = 'draft'
                _logger.info('Updated exercise %s state to draft', exercise.name)
        
        # Exemple : mettre à jour les crédits sans état
        credits = env['budget.credit'].search([])
        for credit in credits:
            if not credit.state:
                credit.state = 'draft'
                _logger.info('Updated credit %s state to draft', credit.name)
                
    except Exception as e:
        _logger.error('Error updating existing data: %s', str(e))
