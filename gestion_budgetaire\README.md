# 🏛️ Gestion Budgétaire - Module Odoo

## 📋 Description

Ce module offre une solution complète et moderne de gestion budgétaire spécialement conçue pour les établissements publics algériens. Il respecte les normes et réglementations locales tout en offrant une interface intuitive et des fonctionnalités avancées.

## ✨ Fonctionnalités Principales

### 🎯 Gestion des Exercices Budgétaires
- Création et gestion des exercices budgétaires annuels
- Suivi des états (brouillon, ouvert, en clôture, fermé)
- Contrôles de cohérence temporelle
- Historique complet des exercices

### 📊 Nomenclature Budgétaire
- Structure hiérarchique configurable (sections, chapitres, articles, paragraphes, lignes)
- Séparation claire entre dépenses et recettes
- Intégration avec la comptabilité analytique
- Codes conformes aux standards algériens

### 💰 Crédits Budgétaires
- Gestion des crédits initiaux et ajustements
- Contrôle budgétaire en temps réel
- Seuils d'alerte et de blocage configurables
- Calcul automatique des disponibilités

### 📝 Engagements Budgétaires
- Création manuelle ou automatique depuis les commandes d'achat
- Workflow de validation configurable
- Types d'engagement multiples (prise en charge, dépense, économie, etc.)
- Suivi complet du cycle engagement → mandatement → paiement

### 💳 Mandats de Paiement
- Génération automatique ou manuelle depuis les engagements
- Types de paiement configurables (CCP, Trésor, Banque, etc.)
- Workflow de validation et suivi des paiements
- Intégration avec la comptabilité

### 🔄 Ajustements Budgétaires
- Virements de crédits entre postes
- Budgets supplémentaires et rectificatifs
- Rattachements de crédits
- Annulations de crédits

### 📈 Gestion des Recettes
- Titres de perception pour les créances
- Suivi des encaissements
- Gestion des échéances
- Rapports de recouvrement

### 🔗 Intégrations
- **Module Achats** : Création automatique d'engagements depuis les commandes
- **Module Ventes** : Génération de titres de perception depuis les factures clients
- **Module Comptabilité** : Synchronisation avec les écritures comptables
- **Module Notes de frais** : Intégration des remboursements

## 🛠️ Installation

### Prérequis
- Odoo 17.0 ou supérieur
- Modules de base : `base`, `account`, `purchase`, `sale`, `hr_expense`, `analytic`

### Étapes d'installation
1. Télécharger le module dans le répertoire `addons` d'Odoo
2. Redémarrer le serveur Odoo
3. Activer le mode développeur
4. Aller dans **Apps** → **Mettre à jour la liste des applications**
5. Rechercher "Gestion Budgétaire" et cliquer sur **Installer**

## ⚙️ Configuration Initiale

### 1. Configuration des Types
- Aller dans **Gestion Budgétaire** → **Configuration** → **Types**
- Configurer les types d'engagement selon vos besoins
- Configurer les types de paiement disponibles

### 2. Nomenclature Budgétaire
- Aller dans **Gestion Budgétaire** → **Configuration** → **Nomenclature**
- Créer la structure hiérarchique de vos postes budgétaires
- Séparer les postes de dépenses et de recettes

### 3. Exercice Budgétaire
- Aller dans **Gestion Budgétaire** → **Gestion** → **Exercices Budgétaires**
- Créer un nouvel exercice pour l'année en cours
- Définir les dates de début et fin

### 4. Crédits Budgétaires
- Aller dans **Gestion Budgétaire** → **Gestion** → **Crédits Budgétaires**
- Créer les crédits pour chaque poste analytique
- Définir les montants initiaux et les seuils

### 5. Paramètres
- Aller dans **Gestion Budgétaire** → **Configuration** → **Paramètres**
- Configurer les options de contrôle budgétaire
- Définir les seuils par défaut

## 👥 Groupes de Sécurité

| Groupe | Permissions | Description |
|--------|-------------|-------------|
| **Utilisateur Budget** | Lecture | Consultation des données budgétaires |
| **Agent de Saisie** | Lecture/Écriture | Création et modification des engagements et mandats |
| **Contrôleur Budgétaire** | Validation | Validation des engagements, mandats et ajustements |
| **Gestionnaire Budget** | Gestion | Configuration des exercices et crédits |
| **Administrateur Budget** | Complet | Accès complet à toutes les fonctionnalités |

## 📊 Rapports Disponibles

### Rapports Standards
- **Situation Budgétaire** : Vue d'ensemble des crédits par poste
- **Rapport des Engagements** : Détail des engagements par période
- **Rapport des Mandats** : Suivi des paiements
- **Rapport des Recettes** : Analyse des encaissements

### Tableaux de Bord
- Indicateurs clés de performance budgétaire
- Graphiques de consommation par poste
- Alertes et notifications automatiques

## 🔧 Utilisation

### Cycle Budgétaire Standard

1. **Préparation**
   - Créer l'exercice budgétaire
   - Définir la nomenclature
   - Saisir les crédits initiaux

2. **Exécution**
   - Créer les engagements (manuels ou automatiques)
   - Valider les engagements
   - Générer les mandats de paiement
   - Effectuer les paiements

3. **Suivi**
   - Consulter les rapports de situation
   - Effectuer les ajustements nécessaires
   - Suivre les indicateurs de performance

4. **Clôture**
   - Vérifier la cohérence des données
   - Générer les rapports de fin d'exercice
   - Fermer l'exercice

### Workflow des Engagements

```
Brouillon → En attente de validation → Validé → Partiellement mandaté → Totalement mandaté
    ↓                                      ↓
  Annulé                               Annulé (si pas de mandats)
```

### Workflow des Mandats

```
Brouillon → En attente de validation → Validé → Payé
    ↓                                      ↓
  Annulé                               Annulé (si pas payé)
```

## 🚨 Contrôles et Validations

### Contrôles Budgétaires
- Vérification de la disponibilité des crédits
- Respect des seuils d'alerte et de blocage
- Cohérence des dates avec l'exercice
- Validation des montants positifs

### Contrôles de Workflow
- Respect des étapes de validation
- Droits d'accès selon les groupes
- Traçabilité des modifications
- Historique des validations

## 🔍 Dépannage

### Problèmes Courants

**Erreur : "Aucun exercice budgétaire ouvert"**
- Vérifier qu'un exercice est créé et ouvert
- Contrôler les dates de l'exercice

**Erreur : "Crédit insuffisant"**
- Vérifier le montant disponible sur le poste
- Effectuer un ajustement si nécessaire

**Erreur : "Poste budgétaire non analytique"**
- Marquer le poste comme analytique dans la nomenclature
- Créer le crédit budgétaire correspondant

### Logs et Débogage
- Activer le mode développeur pour plus de détails
- Consulter les logs Odoo pour les erreurs techniques
- Utiliser les outils de débogage intégrés

## 📞 Support

### Documentation
- Consulter la documentation intégrée dans Odoo
- Utiliser les tooltips et aides contextuelles

### Communauté
- Forum Odoo Community
- Documentation officielle Odoo
- Groupes d'utilisateurs algériens

## 📄 Licence

Ce module est distribué sous licence **LGPL-3**.

## 🤝 Contribution

Les contributions sont les bienvenues ! Pour contribuer :

1. Fork le projet
2. Créer une branche pour votre fonctionnalité
3. Commiter vos changements
4. Pousser vers la branche
5. Ouvrir une Pull Request

## 📝 Changelog

### Version 1.0.0
- Version initiale
- Gestion complète des exercices budgétaires
- Nomenclature hiérarchique
- Engagements et mandats
- Ajustements budgétaires
- Titres de perception
- Rapports et tableaux de bord
- Intégrations avec les modules Odoo

---

**Développé avec ❤️ pour la communauté Odoo algérienne**
