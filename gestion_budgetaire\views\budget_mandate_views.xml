<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Vue liste des mandats -->
        <record id="view_budget_mandate_tree" model="ir.ui.view">
            <field name="name">budget.mandate.tree</field>
            <field name="model">budget.mandate</field>
            <field name="arch" type="xml">
                <tree string="Mandats de Paiement" decoration-info="state=='draft'"
                      decoration-warning="state=='waiting_validation'" decoration-success="state=='validated'"
                      decoration-primary="state=='paid'" decoration-muted="state=='cancelled'">
                    <field name="name"/>
                    <field name="date"/>
                    <field name="engagement_id"/>
                    <field name="nomenclature_id"/>
                    <field name="partner_id"/>
                    <field name="payment_type_id"/>
                    <field name="amount" widget="monetary"/>
                    <field name="payment_date"/>
                    <field name="state" widget="badge"
                           decoration-info="state=='draft'"
                           decoration-warning="state=='waiting_validation'"
                           decoration-success="state=='validated'"
                           decoration-primary="state=='paid'"
                           decoration-muted="state=='cancelled'"/>
                    <field name="user_id" widget="many2one_avatar_user"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!-- Vue formulaire des mandats -->
        <record id="view_budget_mandate_form" model="ir.ui.view">
            <field name="name">budget.mandate.form</field>
            <field name="model">budget.mandate</field>
            <field name="arch" type="xml">
                <form string="Mandat de Paiement">
                    <header>
                        <button name="action_submit_for_validation" string="Soumettre" type="object"
                                class="oe_highlight" invisible="state != 'draft'"
                                groups="gestion_budgetaire.group_budget_agent"/>
                        <button name="action_validate" string="Valider" type="object"
                                class="oe_highlight" invisible="state != 'waiting_validation'"
                                groups="gestion_budgetaire.group_budget_controller"/>
                        <button name="action_mark_as_paid" string="Marquer comme payé" type="object"
                                class="oe_highlight" invisible="state != 'validated'"
                                groups="gestion_budgetaire.group_budget_agent"/>
                        <button name="action_create_payment" string="Créer Paiement Comptable" type="object"
                                invisible="state != 'validated' or payment_id"
                                groups="gestion_budgetaire.group_budget_agent"/>
                        <button name="action_cancel" string="Annuler" type="object"
                                invisible="state in ('paid', 'cancelled')"
                                groups="gestion_budgetaire.group_budget_controller"/>
                        <button name="action_reset_to_draft" string="Remettre en brouillon" type="object"
                                invisible="state not in ('waiting_validation', 'cancelled')"
                                groups="gestion_budgetaire.group_budget_agent"/>
                        <field name="state" widget="statusbar"
                               statusbar_visible="draft,waiting_validation,validated,paid"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_engagement" type="object"
                                    class="oe_stat_button" icon="fa-file-text-o">
                                <div class="o_stat_info">
                                    <span class="o_stat_text">Engagement</span>
                                </div>
                            </button>
                            <button name="action_view_payment" type="object"
                                    class="oe_stat_button" icon="fa-money"
                                    invisible="not payment_id">
                                <div class="o_stat_info">
                                    <span class="o_stat_text">Paiement</span>
                                </div>
                            </button>
                        </div>

                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>

                        <group>
                            <group>
                                <field name="date" readonly="state in ('validated', 'paid', 'cancelled')"/>
                                <field name="engagement_id" readonly="state != 'draft'"/>
                                <field name="exercise_id" readonly="1"/>
                                <field name="nomenclature_id" readonly="1"/>
                            </group>
                            <group>
                                <field name="partner_id" readonly="state in ('validated', 'paid', 'cancelled')"/>
                                <field name="partner_bank_id" readonly="state in ('validated', 'paid', 'cancelled')"/>
                                <field name="payment_type_id" readonly="state in ('validated', 'paid', 'cancelled')"/>
                                <field name="amount" widget="monetary" readonly="state in ('validated', 'paid', 'cancelled')"/>
                                <field name="payment_date" readonly="state != 'validated'"/>
                                <field name="user_id"/>
                                <field name="currency_id" invisible="1"/>
                                <field name="company_id" invisible="not context.get('show_company', False)"/>
                            </group>
                        </group>

                        <group string="Description">
                            <field name="description" nolabel="1" placeholder="Description du mandat"
                                   readonly="state in ('validated', 'paid', 'cancelled')"/>
                        </group>

                        <group string="Référence de Paiement" invisible="not payment_reference">
                            <field name="payment_reference" nolabel="1" placeholder="Référence externe du paiement"
                                   readonly="state in ('paid', 'cancelled')"/>
                        </group>

                        <group string="Documents Liés" invisible="not invoice_id and not payment_id">
                            <group>
                                <field name="invoice_id" readonly="1"/>
                            </group>
                            <group>
                                <field name="payment_id" readonly="1"/>
                            </group>
                        </group>

                        <group string="Suivi" invisible="state == 'draft'">
                            <group>
                                <field name="validated_by" readonly="1"/>
                                <field name="validated_date" readonly="1"/>
                            </group>
                            <group invisible="state != 'paid'">
                                <field name="paid_by" readonly="1"/>
                                <field name="paid_date" readonly="1"/>
                            </group>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Vue kanban des mandats -->
        <record id="view_budget_mandate_kanban" model="ir.ui.view">
            <field name="name">budget.mandate.kanban</field>
            <field name="model">budget.mandate</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" default_group_by="state">
                    <field name="name"/>
                    <field name="date"/>
                    <field name="partner_id"/>
                    <field name="payment_type_id"/>
                    <field name="amount"/>
                    <field name="state"/>
                    <field name="currency_id"/>
                    <field name="user_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="date"/> - <field name="partner_id"/>
                                        </small>
                                    </div>
                                    <span class="o_kanban_record_top_right">
                                        <field name="user_id" widget="many2one_avatar_user"/>
                                    </span>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-12">
                                            <strong><field name="payment_type_id"/></strong>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <span>Montant:</span><br/>
                                            <strong><field name="amount" widget="monetary"/></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Vue recherche des mandats -->
        <record id="view_budget_mandate_search" model="ir.ui.view">
            <field name="name">budget.mandate.search</field>
            <field name="model">budget.mandate</field>
            <field name="arch" type="xml">
                <search string="Rechercher Mandats">
                    <field name="name"/>
                    <field name="engagement_id"/>
                    <field name="partner_id"/>
                    <field name="payment_type_id"/>
                    <field name="user_id"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <separator/>
                    <filter string="Mes mandats" name="my_mandates" domain="[('user_id', '=', uid)]"/>
                    <separator/>
                    <filter string="Brouillon" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="En attente" name="waiting" domain="[('state', '=', 'waiting_validation')]"/>
                    <filter string="Validé" name="validated" domain="[('state', '=', 'validated')]"/>
                    <filter string="Payé" name="paid" domain="[('state', '=', 'paid')]"/>
                    <filter string="Annulé" name="cancelled" domain="[('state', '=', 'cancelled')]"/>
                    <separator/>
                    <filter string="À payer" name="to_pay" domain="[('state', '=', 'validated')]"/>
                    <separator/>
                    <filter string="Ce mois" name="this_month"
                            domain="[('date', '&gt;=', context_today().strftime('%Y-%m-01')), ('date', '&lt;', (context_today() + relativedelta(months=1)).strftime('%Y-%m-01'))]"/>
                    <filter string="Exercice actuel" name="current_exercise"
                            domain="[('exercise_id.state', '=', 'open')]"/>
                    <separator/>
                    <group expand="0" string="Grouper par">
                        <filter string="État" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Engagement" name="group_engagement" context="{'group_by': 'engagement_id'}"/>
                        <filter string="Tiers" name="group_partner" context="{'group_by': 'partner_id'}"/>
                        <filter string="Type de paiement" name="group_payment_type" context="{'group_by': 'payment_type_id'}"/>
                        <filter string="Responsable" name="group_user" context="{'group_by': 'user_id'}"/>
                        <filter string="Date" name="group_date" context="{'group_by': 'date:month'}"/>
                        <filter string="Société" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Action pour les mandats -->
        <record id="action_budget_mandate" model="ir.actions.act_window">
            <field name="name">Mandats de Paiement</field>
            <field name="res_model">budget.mandate</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_mandate_search"/>
            <field name="context">{'search_default_current_exercise': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Créer un nouveau mandat de paiement
                </p>
                <p>
                    Les mandats de paiement représentent les ordres de paiement
                    émis pour liquider les engagements budgétaires.
                </p>
                <p>
                    Ils peuvent être créés manuellement ou automatiquement
                    depuis les engagements validés.
                </p>
            </field>
        </record>

        <!-- Action pour les mandats à valider -->
        <record id="action_budget_mandate_to_validate" model="ir.actions.act_window">
            <field name="name">Mandats à Valider</field>
            <field name="res_model">budget.mandate</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_mandate_search"/>
            <field name="domain">[('state', '=', 'waiting_validation')]</field>
            <field name="context">{'search_default_waiting': 1}</field>
        </record>

        <!-- Action pour les mandats à payer -->
        <record id="action_budget_mandate_to_pay" model="ir.actions.act_window">
            <field name="name">Mandats à Payer</field>
            <field name="res_model">budget.mandate</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_mandate_search"/>
            <field name="domain">[('state', '=', 'validated')]</field>
            <field name="context">{'search_default_to_pay': 1}</field>
        </record>

    </data>
</odoo>
