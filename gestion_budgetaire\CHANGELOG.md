# Changelog - Gestion Budgétaire

Toutes les modifications notables de ce projet seront documentées dans ce fichier.

Le format est basé sur [Keep a Changelog](https://keepachangelog.com/fr/1.0.0/),
et ce projet adhère au [Versioning Sémantique](https://semver.org/lang/fr/).

## [Non publié]

### À venir
- Tableaux de bord interactifs
- API REST pour intégrations externes
- Module de prévisions budgétaires
- Export Excel avancé
- Notifications par email

## [1.0.0] - 2024-01-15

### Ajouté
- **Gestion des exercices budgétaires**
  - Création et gestion des exercices annuels
  - Workflow complet (brouillon → ouvert → clôture → fermé)
  - Contrôles de cohérence temporelle
  - Calcul automatique des totaux

- **Nomenclature budgétaire hiérarchique**
  - Structure configurable (sections, chapitres, articles, paragraphes, lignes)
  - Séparation dépenses/recettes
  - Codes conformes aux standards algériens
  - Intégration avec la comptabilité analytique

- **Crédits budgétaires**
  - Gestion des crédits initiaux et ajustements
  - Contrôle budgétaire en temps réel
  - Seuils d'alerte et de blocage configurables
  - Calcul automatique des disponibilités
  - Indicateurs de performance (taux de consommation, taux de paiement)

- **Engagements budgétaires**
  - Création manuelle ou automatique depuis les commandes d'achat
  - Workflow de validation configurable
  - Types d'engagement multiples (prise en charge, dépense, économie, avoir, etc.)
  - Suivi complet du cycle engagement → mandatement → paiement
  - Contrôle de disponibilité budgétaire

- **Mandats de paiement**
  - Génération automatique ou manuelle depuis les engagements
  - Types de paiement configurables (CCP, Trésor, Banque, Espèces, Chèque)
  - Workflow de validation et suivi des paiements
  - Intégration avec la comptabilité
  - Gestion des références de paiement

- **Ajustements budgétaires**
  - Virements de crédits entre postes
  - Budgets supplémentaires et rectificatifs
  - Rattachements de crédits de l'exercice précédent
  - Annulations de crédits
  - Workflow de validation spécifique

- **Gestion des recettes**
  - Titres de perception pour les créances
  - Suivi des encaissements partiels et totaux
  - Gestion des échéances et relances
  - Rapports de recouvrement
  - États détaillés (brouillon, émis, recouvré, annulé)

- **Intégrations natives**
  - **Module Achats** : Création automatique d'engagements depuis les commandes
  - **Module Ventes** : Génération de titres de perception depuis les factures clients
  - **Module Comptabilité** : Synchronisation avec les écritures comptables
  - **Module Notes de frais** : Intégration des remboursements
  - **Produits** : Imputation budgétaire par défaut

- **Rapports et analyses**
  - Rapport de situation budgétaire par poste
  - Rapport détaillé des engagements
  - Rapport des mandats de paiement
  - Analyse des recettes et encaissements
  - Vues analytiques avec groupements multiples
  - Export PDF des rapports principaux

- **Sécurité et droits d'accès**
  - 5 niveaux de groupes utilisateurs
  - Règles d'accès multi-société
  - Contrôle fin des permissions par état
  - Traçabilité complète des modifications

- **Assistants et outils**
  - Assistant de création d'ajustements budgétaires
  - Assistant d'import de données (préparé pour CSV/Excel)
  - Wizards de configuration initiale

- **Interface utilisateur**
  - Vues adaptées aux écrans mobiles (kanban responsive)
  - Indicateurs visuels (barres de progression, badges colorés)
  - Filtres et groupements avancés
  - Recherche intelligente multi-critères

- **Configuration et paramétrage**
  - Types d'engagement configurables
  - Types de paiement configurables
  - Seuils d'alerte personnalisables
  - Paramètres de contrôle budgétaire
  - Séquences automatiques pour tous les documents

### Technique
- **Architecture modulaire** respectant les standards Odoo 17
- **Tests unitaires** pour les fonctionnalités critiques
- **Hooks d'installation** pour la configuration automatique
- **Scripts de migration** pour les mises à jour futures
- **Documentation technique** complète
- **Données de démonstration** pour les tests
- **Styles CSS** personnalisés pour l'interface
- **Widgets JavaScript** pour les fonctionnalités avancées

### Sécurité
- Validation stricte des données d'entrée
- Contrôles de cohérence métier
- Protection contre les dépassements budgétaires
- Audit trail complet des opérations

### Performance
- Champs calculés optimisés
- Index de base de données appropriés
- Requêtes SQL optimisées pour les rapports
- Calculs en lot pour les gros volumes

### Conformité
- Respect des normes budgétaires algériennes
- Séparation claire dépenses/recettes
- Nomenclature conforme aux standards publics
- Workflow de validation réglementaire

### Documentation
- README complet avec guide d'installation
- Documentation technique détaillée
- Guide utilisateur intégré
- Exemples de configuration

## [0.9.0] - 2024-01-01

### Ajouté
- Version bêta initiale
- Modèles de base
- Vues principales
- Tests préliminaires

### Modifié
- Architecture des modèles
- Interface utilisateur
- Workflow de validation

### Corrigé
- Calculs de montants
- Contraintes de validation
- Permissions d'accès

## [0.1.0] - 2023-12-15

### Ajouté
- Projet initial
- Structure de base du module
- Modèles principaux
- Vues de base

---

## Types de changements

- **Ajouté** pour les nouvelles fonctionnalités
- **Modifié** pour les changements dans les fonctionnalités existantes
- **Déprécié** pour les fonctionnalités qui seront supprimées prochainement
- **Supprimé** pour les fonctionnalités supprimées
- **Corrigé** pour les corrections de bugs
- **Sécurité** pour les vulnérabilités corrigées

## Liens

- [Dépôt du projet](https://github.com/votre-organisation/gestion-budgetaire)
- [Signaler un bug](https://github.com/votre-organisation/gestion-budgetaire/issues)
- [Demander une fonctionnalité](https://github.com/votre-organisation/gestion-budgetaire/issues)
- [Documentation](https://github.com/votre-organisation/gestion-budgetaire/wiki)
