<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Vue liste des exercices budgétaires -->
        <record id="view_budget_exercise_tree" model="ir.ui.view">
            <field name="name">budget.exercise.tree</field>
            <field name="model">budget.exercise</field>
            <field name="arch" type="xml">
                <tree string="Exercices Budgétaires" decoration-info="state=='draft'" 
                      decoration-success="state=='open'" decoration-muted="state=='closed'">
                    <field name="name"/>
                    <field name="code"/>
                    <field name="date_start"/>
                    <field name="date_end"/>
                    <field name="state" widget="badge" 
                           decoration-info="state=='draft'"
                           decoration-success="state=='open'"
                           decoration-warning="state=='closing'"
                           decoration-muted="state=='closed'"/>
                    <field name="total_credits_initial" widget="monetary"/>
                    <field name="total_credits_voted" widget="monetary"/>
                    <field name="total_engagements" widget="monetary"/>
                    <field name="total_available_credits" widget="monetary"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <field name="currency_id" invisible="1"/>
                </tree>
            </field>
        </record>
        
        <!-- Vue formulaire des exercices budgétaires -->
        <record id="view_budget_exercise_form" model="ir.ui.view">
            <field name="name">budget.exercise.form</field>
            <field name="model">budget.exercise</field>
            <field name="arch" type="xml">
                <form string="Exercice Budgétaire">
                    <header>
                        <button name="action_open" string="Ouvrir" type="object" 
                                class="oe_highlight" invisible="state != 'draft'"/>
                        <button name="action_set_closing" string="Mettre en clôture" type="object" 
                                invisible="state != 'open'"/>
                        <button name="action_close" string="Fermer" type="object" 
                                invisible="state != 'closing'"/>
                        <button name="action_reopen" string="Rouvrir" type="object" 
                                invisible="state != 'closed'" groups="gestion_budgetaire.group_budget_admin"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,open,closing,closed"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="Nom de l'exercice"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="code"/>
                                <field name="date_start"/>
                                <field name="date_end"/>
                                <field name="company_id" groups="base.group_multi_company"/>
                            </group>
                            <group>
                                <field name="currency_id" invisible="1"/>
                                <field name="total_credits_initial" widget="monetary"/>
                                <field name="total_credits_voted" widget="monetary"/>
                                <field name="total_engagements" widget="monetary"/>
                                <field name="total_available_credits" widget="monetary"/>
                            </group>
                        </group>
                        <field name="description" placeholder="Description de l'exercice budgétaire"/>
                        
                        <notebook>
                            <page string="Crédits Budgétaires" name="credits">
                                <field name="credit_ids" readonly="state == 'closed'">
                                    <tree editable="bottom">
                                        <field name="nomenclature_id"/>
                                        <field name="amount_initial" widget="monetary"/>
                                        <field name="amount_adjustments" widget="monetary"/>
                                        <field name="amount_voted" widget="monetary"/>
                                        <field name="amount_engaged" widget="monetary"/>
                                        <field name="amount_available" widget="monetary"/>
                                        <field name="consumption_rate" widget="percentage"/>
                                        <field name="state" widget="badge"/>
                                        <field name="currency_id" invisible="1"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Ajustements" name="adjustments">
                                <field name="adjustment_ids" readonly="state == 'closed'">
                                    <tree>
                                        <field name="name"/>
                                        <field name="date"/>
                                        <field name="adjustment_type"/>
                                        <field name="total_debit" widget="monetary"/>
                                        <field name="total_credit" widget="monetary"/>
                                        <field name="balance" widget="monetary"/>
                                        <field name="state" widget="badge"/>
                                        <field name="currency_id" invisible="1"/>
                                    </tree>
                                </field>
                            </page>
                            <page string="Engagements" name="engagements">
                                <field name="engagement_ids" readonly="state == 'closed'">
                                    <tree>
                                        <field name="name"/>
                                        <field name="date"/>
                                        <field name="nomenclature_id"/>
                                        <field name="partner_id"/>
                                        <field name="amount" widget="monetary"/>
                                        <field name="amount_mandated" widget="monetary"/>
                                        <field name="amount_remaining" widget="monetary"/>
                                        <field name="state" widget="badge"/>
                                        <field name="currency_id" invisible="1"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>
        
        <!-- Vue kanban des exercices budgétaires -->
        <record id="view_budget_exercise_kanban" model="ir.ui.view">
            <field name="name">budget.exercise.kanban</field>
            <field name="model">budget.exercise</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile">
                    <field name="name"/>
                    <field name="code"/>
                    <field name="state"/>
                    <field name="date_start"/>
                    <field name="date_end"/>
                    <field name="total_credits_voted"/>
                    <field name="total_engagements"/>
                    <field name="total_available_credits"/>
                    <field name="currency_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="code"/> - 
                                            <field name="date_start"/> / <field name="date_end"/>
                                        </small>
                                    </div>
                                    <span class="o_kanban_record_top_right">
                                        <field name="state" widget="label_selection" 
                                               options="{'classes': {'draft': 'info', 'open': 'success', 'closing': 'warning', 'closed': 'default'}}"/>
                                    </span>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-6">
                                            <span>Crédits votés:</span><br/>
                                            <strong><field name="total_credits_voted" widget="monetary"/></strong>
                                        </div>
                                        <div class="col-6">
                                            <span>Engagements:</span><br/>
                                            <strong><field name="total_engagements" widget="monetary"/></strong>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <span>Disponible:</span><br/>
                                            <strong><field name="total_available_credits" widget="monetary"/></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>
        
        <!-- Vue recherche des exercices budgétaires -->
        <record id="view_budget_exercise_search" model="ir.ui.view">
            <field name="name">budget.exercise.search</field>
            <field name="model">budget.exercise</field>
            <field name="arch" type="xml">
                <search string="Rechercher Exercices">
                    <field name="name" string="Nom ou Code" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                    <field name="code"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <separator/>
                    <filter string="Brouillon" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Ouvert" name="open" domain="[('state', '=', 'open')]"/>
                    <filter string="En clôture" name="closing" domain="[('state', '=', 'closing')]"/>
                    <filter string="Fermé" name="closed" domain="[('state', '=', 'closed')]"/>
                    <separator/>
                    <filter string="Exercice actuel" name="current_year" 
                            domain="[('date_start', '&lt;=', context_today().strftime('%Y-%m-%d')), ('date_end', '&gt;=', context_today().strftime('%Y-%m-%d'))]"/>
                    <separator/>
                    <group expand="0" string="Grouper par">
                        <filter string="État" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Société" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                        <filter string="Année" name="group_year" context="{'group_by': 'date_start:year'}"/>
                    </group>
                </search>
            </field>
        </record>
        
        <!-- Action pour les exercices budgétaires -->
        <record id="action_budget_exercise" model="ir.actions.act_window">
            <field name="name">Exercices Budgétaires</field>
            <field name="res_model">budget.exercise</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_exercise_search"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Créer un nouvel exercice budgétaire
                </p>
                <p>
                    Un exercice budgétaire définit la période de gestion du budget.
                    Il contient tous les crédits, engagements et ajustements de la période.
                </p>
            </field>
        </record>
        
    </data>
</odoo>
