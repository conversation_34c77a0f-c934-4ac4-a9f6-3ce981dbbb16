# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
from datetime import datetime
import logging

_logger = logging.getLogger(__name__)


class BudgetPerceptionTitle(models.Model):
    """Modèle pour la gestion des titres de perception (recettes)"""
    
    _name = 'budget.perception.title'
    _description = 'Titre de Perception'
    _order = 'date desc, name desc'
    _check_company_auto = True
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    # Champs de base
    name = fields.Char(
        string='Référence',
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: _('Nouveau'),
        help='Référence unique du titre de perception'
    )
    
    date = fields.Date(
        string='Date d\'émission',
        required=True,
        default=fields.Date.context_today,
        tracking=True,
        help='Date d\'émission du titre de perception'
    )
    
    due_date = fields.Date(
        string='Date d\'échéance',
        help='Date limite de recouvrement'
    )
    
    exercise_id = fields.Many2one(
        'budget.exercise',
        string='Exercice budgétaire',
        required=True,
        domain="[('state', '=', 'open'), ('company_id', '=', company_id)]",
        help='Exercice budgétaire concerné'
    )
    
    nomenclature_id = fields.Many2one(
        'budget.nomenclature',
        string='Poste budgétaire',
        required=True,
        domain="[('is_analytical', '=', True), ('budget_type', '=', 'revenue'), ('company_id', '=', company_id)]",
        help='Poste budgétaire de recette'
    )
    
    # Montant et devise
    amount = fields.Monetary(
        string='Montant à percevoir',
        currency_field='currency_id',
        required=True,
        tracking=True,
        help='Montant total à percevoir'
    )
    
    amount_collected = fields.Monetary(
        string='Montant encaissé',
        compute='_compute_amounts',
        store=True,
        currency_field='currency_id',
        help='Montant déjà encaissé'
    )
    
    amount_remaining = fields.Monetary(
        string='Reste à encaisser',
        compute='_compute_amounts',
        store=True,
        currency_field='currency_id',
        help='Montant restant à encaisser'
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Société',
        required=True,
        default=lambda self: self.env.company,
        help='Société concernée'
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Devise',
        related='company_id.currency_id',
        store=True,
        readonly=True
    )
    
    # Débiteur
    partner_id = fields.Many2one(
        'res.partner',
        string='Débiteur',
        required=True,
        help='Client ou débiteur'
    )
    
    # État et workflow
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('issued', 'Émis'),
        ('partially_collected', 'Partiellement recouvré'),
        ('fully_collected', 'Totalement recouvré'),
        ('cancelled', 'Annulé'),
    ], string='État', default='draft', required=True, tracking=True,
       help='État du titre de perception')
    
    # Documents sources
    invoice_id = fields.Many2one(
        'account.move',
        string='Facture client',
        help='Facture client source'
    )
    
    sale_order_id = fields.Many2one(
        'sale.order',
        string='Commande de vente',
        help='Commande de vente source'
    )
    
    # Description
    description = fields.Text(
        string='Description',
        required=True,
        help='Description du titre de perception'
    )
    
    reason = fields.Text(
        string='Motif',
        help='Motif ou justification du titre'
    )
    
    # Relations
    collection_ids = fields.One2many(
        'budget.perception.collection',
        'title_id',
        string='Encaissements',
        help='Encaissements liés à ce titre'
    )
    
    # Champs de suivi
    user_id = fields.Many2one(
        'res.users',
        string='Responsable',
        default=lambda self: self.env.user,
        help='Utilisateur responsable du titre'
    )
    
    issued_by = fields.Many2one(
        'res.users',
        string='Émis par',
        readonly=True,
        help='Utilisateur ayant émis le titre'
    )
    
    issued_date = fields.Datetime(
        string='Date d\'émission',
        readonly=True,
        help='Date et heure d\'émission'
    )
    
    # Contraintes
    @api.constrains('amount')
    def _check_amount_positive(self):
        """Vérifier que le montant est positif"""
        for record in self:
            if record.amount <= 0:
                raise ValidationError(
                    _('Le montant du titre de perception doit être positif.')
                )
    
    @api.constrains('date', 'exercise_id')
    def _check_date_in_exercise(self):
        """Vérifier que la date est dans l'exercice"""
        for record in self:
            if not (record.exercise_id.date_start <= record.date <= record.exercise_id.date_end):
                raise ValidationError(
                    _('La date d\'émission doit être comprise dans l\'exercice budgétaire.')
                )
    
    @api.constrains('due_date', 'date')
    def _check_due_date(self):
        """Vérifier que la date d'échéance est postérieure à la date d'émission"""
        for record in self:
            if record.due_date and record.due_date < record.date:
                raise ValidationError(
                    _('La date d\'échéance doit être postérieure à la date d\'émission.')
                )
    
    # Méthodes de calcul
    @api.depends('collection_ids.amount', 'collection_ids.state')
    def _compute_amounts(self):
        """Calculer les montants encaissés"""
        for record in self:
            collected_amount = sum(
                collection.amount for collection in record.collection_ids
                if collection.state == 'collected'
            )
            record.amount_collected = collected_amount
            record.amount_remaining = record.amount - collected_amount
    
    # Méthodes CRUD
    @api.model_create_multi
    def create(self, vals_list):
        """Créer un titre avec numérotation automatique"""
        for vals in vals_list:
            if vals.get('name', _('Nouveau')) == _('Nouveau'):
                vals['name'] = self.env['ir.sequence'].next_by_code('budget.perception.title') or _('Nouveau')
        
        return super().create(vals_list)
    
    # Actions de workflow
    def action_issue(self):
        """Émettre le titre de perception"""
        for record in self:
            if record.state != 'draft':
                raise UserError(_('Seul un titre en brouillon peut être émis.'))
            
            record.write({
                'state': 'issued',
                'issued_by': self.env.user.id,
                'issued_date': datetime.now(),
            })
            
            _logger.info('Titre de perception %s émis par %s', record.name, self.env.user.name)
    
    def action_cancel(self):
        """Annuler le titre de perception"""
        for record in self:
            if record.state == 'fully_collected':
                raise UserError(_('Un titre totalement recouvré ne peut pas être annulé.'))
            
            if record.collection_ids.filtered(lambda c: c.state == 'collected'):
                raise UserError(_('Impossible d\'annuler un titre avec des encaissements.'))
            
            # Annuler les encaissements en attente
            record.collection_ids.filtered(lambda c: c.state != 'collected').action_cancel()
            
            record.state = 'cancelled'
            _logger.info('Titre de perception %s annulé', record.name)
    
    def action_reset_to_draft(self):
        """Remettre en brouillon"""
        for record in self:
            if record.state not in ('issued', 'cancelled'):
                raise UserError(_('Seul un titre émis ou annulé peut être remis en brouillon.'))
            
            if record.collection_ids:
                raise UserError(_('Impossible de remettre en brouillon un titre avec des encaissements.'))
            
            record.state = 'draft'
    
    # Actions de vue
    def action_view_collections(self):
        """Voir les encaissements de ce titre"""
        self.ensure_one()
        return {
            'name': _('Encaissements'),
            'type': 'ir.actions.act_window',
            'res_model': 'budget.perception.collection',
            'view_mode': 'tree,form',
            'domain': [('title_id', '=', self.id)],
            'context': {'default_title_id': self.id},
        }
    
    def action_create_collection(self):
        """Créer un nouvel encaissement"""
        self.ensure_one()
        
        if self.state != 'issued':
            raise UserError(_('Seul un titre émis peut générer un encaissement.'))
        
        if self.amount_remaining <= 0:
            raise UserError(_('Ce titre est déjà totalement recouvré.'))
        
        return {
            'name': _('Créer un encaissement'),
            'type': 'ir.actions.act_window',
            'res_model': 'budget.perception.collection',
            'view_mode': 'form',
            'context': {
                'default_title_id': self.id,
                'default_amount': self.amount_remaining,
                'default_partner_id': self.partner_id.id,
                'default_description': f"Encaissement pour {self.description}",
            },
            'target': 'new',
        }
    
    # Méthodes utilitaires
    @api.onchange('amount_remaining')
    def _onchange_state_from_collections(self):
        """Mettre à jour l'état selon les encaissements"""
        for record in self:
            if record.state == 'issued':
                if record.amount_collected == 0:
                    continue  # Reste émis
                elif record.amount_remaining > 0:
                    record.state = 'partially_collected'
                else:
                    record.state = 'fully_collected'


class BudgetPerceptionCollection(models.Model):
    """Modèle pour les encaissements de titres de perception"""
    
    _name = 'budget.perception.collection'
    _description = 'Encaissement de Titre de Perception'
    _order = 'date desc, name desc'
    _check_company_auto = True
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    # Champs de base
    name = fields.Char(
        string='Référence',
        required=True,
        copy=False,
        readonly=True,
        default=lambda self: _('Nouveau'),
        help='Référence unique de l\'encaissement'
    )
    
    date = fields.Date(
        string='Date d\'encaissement',
        required=True,
        default=fields.Date.context_today,
        tracking=True,
        help='Date de l\'encaissement'
    )
    
    title_id = fields.Many2one(
        'budget.perception.title',
        string='Titre de perception',
        required=True,
        domain="[('state', '=', 'issued'), ('company_id', '=', company_id)]",
        help='Titre de perception source'
    )
    
    # Montant
    amount = fields.Monetary(
        string='Montant encaissé',
        currency_field='currency_id',
        required=True,
        tracking=True,
        help='Montant de l\'encaissement'
    )
    
    # Champs liés
    exercise_id = fields.Many2one(
        'budget.exercise',
        string='Exercice',
        related='title_id.exercise_id',
        store=True,
        readonly=True
    )
    
    nomenclature_id = fields.Many2one(
        'budget.nomenclature',
        string='Poste budgétaire',
        related='title_id.nomenclature_id',
        store=True,
        readonly=True
    )
    
    partner_id = fields.Many2one(
        'res.partner',
        string='Débiteur',
        related='title_id.partner_id',
        store=True,
        readonly=True
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Société',
        related='title_id.company_id',
        store=True,
        readonly=True
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Devise',
        related='company_id.currency_id',
        store=True,
        readonly=True
    )
    
    # État
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('collected', 'Encaissé'),
        ('cancelled', 'Annulé'),
    ], string='État', default='draft', required=True, tracking=True,
       help='État de l\'encaissement')
    
    # Paiement lié
    payment_id = fields.Many2one(
        'account.payment',
        string='Paiement comptable',
        help='Paiement comptable lié'
    )
    
    # Description
    description = fields.Text(
        string='Description',
        required=True,
        help='Description de l\'encaissement'
    )
    
    # Contraintes
    @api.constrains('amount')
    def _check_amount_positive(self):
        """Vérifier que le montant est positif"""
        for record in self:
            if record.amount <= 0:
                raise ValidationError(
                    _('Le montant de l\'encaissement doit être positif.')
                )
    
    @api.constrains('amount', 'title_id')
    def _check_amount_vs_title(self):
        """Vérifier que le montant ne dépasse pas le reste à encaisser"""
        for record in self:
            if record.title_id:
                # Calculer le total des autres encaissements de ce titre
                other_collections_amount = sum(
                    collection.amount for collection in record.title_id.collection_ids
                    if collection.id != record.id and collection.state != 'cancelled'
                )
                
                total_collected = other_collections_amount + record.amount
                
                if total_collected > record.title_id.amount:
                    raise ValidationError(
                        _('Le montant total des encaissements (%s) dépasse le montant du titre (%s).') %
                        (total_collected, record.title_id.amount)
                    )
    
    # Méthodes CRUD
    @api.model_create_multi
    def create(self, vals_list):
        """Créer un encaissement avec numérotation automatique"""
        for vals in vals_list:
            if vals.get('name', _('Nouveau')) == _('Nouveau'):
                vals['name'] = self.env['ir.sequence'].next_by_code('budget.perception.collection') or _('Nouveau')
        
        return super().create(vals_list)
    
    # Actions de workflow
    def action_collect(self):
        """Marquer comme encaissé"""
        for record in self:
            if record.state != 'draft':
                raise UserError(_('Seul un encaissement en brouillon peut être marqué comme encaissé.'))
            
            record.state = 'collected'
            
            # Mettre à jour l'état du titre
            record.title_id._onchange_state_from_collections()
            
            _logger.info('Encaissement %s marqué comme encaissé', record.name)
    
    def action_cancel(self):
        """Annuler l'encaissement"""
        for record in self:
            if record.state == 'collected':
                raise UserError(_('Un encaissement encaissé ne peut pas être annulé.'))
            
            record.state = 'cancelled'
            _logger.info('Encaissement %s annulé', record.name)
    
    def action_reset_to_draft(self):
        """Remettre en brouillon"""
        for record in self:
            if record.state != 'cancelled':
                raise UserError(_('Seul un encaissement annulé peut être remis en brouillon.'))
            record.state = 'draft'
