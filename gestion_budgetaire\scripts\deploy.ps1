# Script de déploiement PowerShell pour le module Gestion Budgétaire
# Usage: .\deploy.ps1 -Environment [dev|test|prod] -Action [install|upgrade|test]

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("dev", "test", "prod")]
    [string]$Environment = "dev",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("install", "upgrade", "test")]
    [string]$Action = "install"
)

# Configuration
$ModuleName = "gestion_budgetaire"
$ErrorActionPreference = "Stop"

# Fonction d'affichage avec couleurs
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    switch ($Level) {
        "ERROR" { Write-Host "[$timestamp] [ERROR] $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "[$timestamp] [WARNING] $Message" -ForegroundColor Yellow }
        "SUCCESS" { Write-Host "[$timestamp] [SUCCESS] $Message" -ForegroundColor Green }
        default { Write-Host "[$timestamp] [INFO] $Message" -ForegroundColor Blue }
    }
}

# Configuration selon l'environnement
switch ($Environment) {
    "dev" {
        $DbName = "gestion_budgetaire_dev"
        $OdooConfig = "C:\odoo\conf\odoo-dev.conf"
        $AddonsPath = "C:\odoo\addons-dev"
    }
    "test" {
        $DbName = "gestion_budgetaire_test"
        $OdooConfig = "C:\odoo\conf\odoo-test.conf"
        $AddonsPath = "C:\odoo\addons-test"
    }
    "prod" {
        $DbName = "gestion_budgetaire_prod"
        $OdooConfig = "C:\odoo\conf\odoo.conf"
        $AddonsPath = "C:\odoo\addons"
    }
}

Write-Log "Déploiement du module $ModuleName en environnement $Environment"

# Vérifications préalables
function Test-Prerequisites {
    Write-Log "Vérification des prérequis..."
    
    # Vérifier que le répertoire addons existe
    if (-not (Test-Path $AddonsPath)) {
        Write-Log "Répertoire addons non trouvé: $AddonsPath" "ERROR"
        exit 1
    }
    
    # Vérifier que le fichier de configuration existe
    if (-not (Test-Path $OdooConfig)) {
        Write-Log "Fichier de configuration non trouvé: $OdooConfig" "ERROR"
        exit 1
    }
    
    Write-Log "Prérequis vérifiés" "SUCCESS"
}

# Copier le module
function Copy-Module {
    Write-Log "Copie du module vers $AddonsPath..."
    
    $ModulePath = Join-Path $AddonsPath $ModuleName
    
    # Créer une sauvegarde si le module existe déjà
    if (Test-Path $ModulePath) {
        $BackupPath = "${ModulePath}.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Write-Log "Module existant trouvé, création d'une sauvegarde..." "WARNING"
        Copy-Item -Path $ModulePath -Destination $BackupPath -Recurse
    }
    
    # Copier le nouveau module
    $SourcePath = Split-Path -Parent $PSScriptRoot
    Copy-Item -Path $SourcePath -Destination $ModulePath -Recurse -Force
    
    # Supprimer les fichiers non nécessaires
    $ItemsToRemove = @(
        (Join-Path $ModulePath ".git"),
        (Join-Path $ModulePath ".vscode"),
        (Join-Path $ModulePath "scripts"),
        (Join-Path $ModulePath ".gitignore"),
        (Join-Path $ModulePath ".odoo_test")
    )
    
    foreach ($item in $ItemsToRemove) {
        if (Test-Path $item) {
            Remove-Item -Path $item -Recurse -Force
        }
    }
    
    Write-Log "Module copié" "SUCCESS"
}

# Installer le module
function Install-Module {
    Write-Log "Installation du module $ModuleName..."
    
    $OdooCmd = "odoo-bin -c `"$OdooConfig`" -d `"$DbName`" -i `"$ModuleName`" --stop-after-init"
    
    try {
        Invoke-Expression $OdooCmd
        Write-Log "Module installé avec succès" "SUCCESS"
    }
    catch {
        Write-Log "Échec de l'installation du module: $($_.Exception.Message)" "ERROR"
        exit 1
    }
}

# Mettre à jour le module
function Update-Module {
    Write-Log "Mise à jour du module $ModuleName..."
    
    $OdooCmd = "odoo-bin -c `"$OdooConfig`" -d `"$DbName`" -u `"$ModuleName`" --stop-after-init"
    
    try {
        Invoke-Expression $OdooCmd
        Write-Log "Module mis à jour avec succès" "SUCCESS"
    }
    catch {
        Write-Log "Échec de la mise à jour du module: $($_.Exception.Message)" "ERROR"
        exit 1
    }
}

# Exécuter les tests
function Invoke-Tests {
    Write-Log "Exécution des tests pour $ModuleName..."
    
    $TestDb = "${DbName}_test"
    
    # Supprimer la base de test si elle existe
    try {
        dropdb --if-exists $TestDb
    }
    catch {
        # Ignorer l'erreur si la base n'existe pas
    }
    
    # Créer la base de test
    createdb $TestDb
    
    # Exécuter les tests
    $OdooCmd = "odoo-bin -c `"$OdooConfig`" -d `"$TestDb`" -i `"$ModuleName`" --test-enable --stop-after-init"
    
    try {
        Invoke-Expression $OdooCmd
        Write-Log "Tests exécutés avec succès" "SUCCESS"
    }
    catch {
        Write-Log "Échec des tests: $($_.Exception.Message)" "ERROR"
        exit 1
    }
    finally {
        # Nettoyer la base de test
        try {
            dropdb $TestDb
        }
        catch {
            Write-Log "Impossible de supprimer la base de test" "WARNING"
        }
    }
}

# Redémarrer Odoo
function Restart-Odoo {
    Write-Log "Redémarrage d'Odoo..."
    
    try {
        $service = Get-Service -Name "Odoo" -ErrorAction SilentlyContinue
        if ($service -and $service.Status -eq "Running") {
            Restart-Service -Name "Odoo"
            Write-Log "Odoo redémarré" "SUCCESS"
        }
        else {
            Write-Log "Service Odoo non actif" "WARNING"
        }
    }
    catch {
        Write-Log "Impossible de redémarrer Odoo: $($_.Exception.Message)" "WARNING"
    }
}

# Fonction principale
function Main {
    Write-Log "Début du déploiement - Environnement: $Environment, Action: $Action"
    
    Test-Prerequisites
    Copy-Module
    
    switch ($Action) {
        "install" { Install-Module }
        "upgrade" { Update-Module }
        "test" { Invoke-Tests }
    }
    
    if ($Environment -ne "test") {
        Restart-Odoo
    }
    
    Write-Log "Déploiement terminé avec succès!" "SUCCESS"
}

# Exécution du script principal
try {
    Main
}
catch {
    Write-Log "Erreur lors du déploiement: $($_.Exception.Message)" "ERROR"
    exit 1
}
