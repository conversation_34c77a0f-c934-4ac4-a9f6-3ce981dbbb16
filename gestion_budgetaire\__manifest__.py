# -*- coding: utf-8 -*-
{
    'name': 'Gestion Budgétaire',
    'version': '********.0',
    'category': 'Accounting/Accounting',
    'summary': 'Module de gestion budgétaire pour établissements publics algériens',
    'description': """
Gestion Budgétaire pour Établissements Publics Algériens
========================================================

Ce module offre une solution complète de gestion budgétaire adaptée au contexte algérien :

Fonctionnalités principales :
* Gestion des exercices budgétaires
* Nomenclature budgétaire hiérarchique configurable
* Gestion des crédits et ajustements budgétaires
* Contrôle budgétaire en temps réel
* Gestion des engagements et mandatements
* Titres de perception pour les recettes
* Intégration avec les modules Achats, Ventes, Comptabilité et Notes de frais
* Rapports conformes aux exigences réglementaires algériennes
* Workflows de validation configurables
* Support multilingue et multi-sociétés

Conformité :
* Respect des guidelines Odoo 17
* Architecture modulaire et extensible
* Sécurité et contrôles d'accès
* Performance optimisée
    """,
    'author': 'Votre Organisation',
    'website': 'https://www.votre-site.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'mail',
        'account',
        'purchase',
        'sale',
        'hr_expense',
        'analytic',
        'web',
    ],
    'data': [
        # Security
        'security/budget_security.xml',
        'security/ir.model.access.csv',

        # Data
        'data/budget_data.xml',
        'data/budget_sequence.xml',

        # Views
        'views/budget_exercise_views.xml',
        'views/budget_nomenclature_views.xml',
        'views/budget_credit_views.xml',
        'views/budget_adjustment_views.xml',
        'views/budget_engagement_views.xml',
        'views/budget_mandate_views.xml',
        'views/budget_perception_title_views.xml',
        'views/budget_config_views.xml',
        'views/budget_menus.xml',

        # Reports
        'reports/budget_reports.xml',
        'reports/budget_report_templates.xml',

        # Wizards
        'wizards/budget_adjustment_wizard_views.xml',
    ],
    'demo': [
        'demo/budget_demo.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'gestion_budgetaire/static/src/css/budget.css',
            'gestion_budgetaire/static/src/js/budget_widgets.js',
        ],
    },
    'images': ['static/description/icon.png'],
    'installable': True,
    'auto_install': False,
    'application': True,
    'sequence': 10,
    'external_dependencies': {
        'python': [],
    },

    # Hooks d'installation
    'post_init_hook': 'post_init_hook',
    'uninstall_hook': 'uninstall_hook',
}
