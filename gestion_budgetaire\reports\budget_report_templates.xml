<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Template pour le rapport de situation bud<PERSON>taire -->
        <template id="report_budget_situation_document">
            <t t-call="web.external_layout">
                <div class="page">
                    <div class="oe_structure"/>
                    
                    <div class="row">
                        <div class="col-12">
                            <h2>Situation Budgétaire</h2>
                            <p><strong>Exercice :</strong> <span t-field="docs[0].exercise_id.name"/></p>
                            <p><strong>Date d'édition :</strong> <span t-esc="context_timestamp(datetime.datetime.now()).strftime('%d/%m/%Y %H:%M')"/></p>
                        </div>
                    </div>
                    
                    <table class="table table-sm table-bordered">
                        <thead>
                            <tr class="table-active">
                                <th>Code</th>
                                <th>Libellé</th>
                                <th class="text-right">Crédits Initiaux</th>
                                <th class="text-right">Ajustements</th>
                                <th class="text-right">Crédits Votés</th>
                                <th class="text-right">Engagements</th>
                                <th class="text-right">Disponible</th>
                                <th class="text-right">Taux (%)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-foreach="docs" t-as="line">
                                <tr>
                                    <td><span t-field="line.nomenclature_code"/></td>
                                    <td><span t-field="line.nomenclature_name"/></td>
                                    <td class="text-right">
                                        <span t-field="line.amount_initial" 
                                              t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="line.amount_adjustments" 
                                              t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="line.amount_voted" 
                                              t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="line.amount_engaged" 
                                              t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/>
                                    </td>
                                    <td class="text-right" t-att-class="'text-danger' if line.amount_available &lt; 0 else ''">
                                        <span t-field="line.amount_available" 
                                              t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/>
                                    </td>
                                    <td class="text-right" t-att-class="'text-danger' if line.consumption_rate &gt;= 100 else ('text-warning' if line.consumption_rate &gt;= 80 else '')">
                                        <span t-esc="'%.1f' % line.consumption_rate"/>%
                                    </td>
                                </tr>
                            </t>
                        </tbody>
                    </table>
                    
                    <div class="oe_structure"/>
                </div>
            </t>
        </template>
        
        <!-- Rapport de situation budgétaire -->
        <record id="action_report_budget_situation" model="ir.actions.report">
            <field name="name">Situation Budgétaire</field>
            <field name="model">budget.situation.report</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">gestion_budgetaire.report_budget_situation_document</field>
            <field name="report_file">gestion_budgetaire.report_budget_situation_document</field>
            <field name="binding_model_id" ref="model_budget_situation_report"/>
            <field name="binding_type">report</field>
        </record>
        
        <!-- Template pour le rapport d'engagement -->
        <template id="report_budget_engagement_document">
            <t t-call="web.external_layout">
                <div class="page">
                    <div class="oe_structure"/>
                    
                    <div class="row">
                        <div class="col-12">
                            <h2>Rapport des Engagements</h2>
                            <p><strong>Période :</strong> Du <span t-esc="date_from"/> au <span t-esc="date_to"/></p>
                            <p><strong>Date d'édition :</strong> <span t-esc="context_timestamp(datetime.datetime.now()).strftime('%d/%m/%Y %H:%M')"/></p>
                        </div>
                    </div>
                    
                    <table class="table table-sm table-bordered">
                        <thead>
                            <tr class="table-active">
                                <th>Date</th>
                                <th>Poste Budgétaire</th>
                                <th>Type</th>
                                <th>Tiers</th>
                                <th class="text-right">Montant</th>
                                <th class="text-right">Mandaté</th>
                                <th class="text-right">Reste</th>
                                <th>État</th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-foreach="docs" t-as="line">
                                <tr>
                                    <td><span t-field="line.date"/></td>
                                    <td><span t-field="line.nomenclature_id.complete_code"/> - <span t-field="line.nomenclature_id.name"/></td>
                                    <td><span t-field="line.engagement_type_id.name"/></td>
                                    <td><span t-field="line.partner_id.name"/></td>
                                    <td class="text-right">
                                        <span t-field="line.amount" 
                                              t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="line.amount_mandated" 
                                              t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/>
                                    </td>
                                    <td class="text-right">
                                        <span t-field="line.amount_remaining" 
                                              t-options="{'widget': 'monetary', 'display_currency': line.currency_id}"/>
                                    </td>
                                    <td>
                                        <span t-if="line.state == 'draft'" class="badge badge-info">Brouillon</span>
                                        <span t-if="line.state == 'waiting_validation'" class="badge badge-warning">En attente</span>
                                        <span t-if="line.state == 'validated'" class="badge badge-success">Validé</span>
                                        <span t-if="line.state == 'partially_mandated'" class="badge badge-primary">Part. mandaté</span>
                                        <span t-if="line.state == 'fully_mandated'" class="badge badge-primary">Tot. mandaté</span>
                                        <span t-if="line.state == 'cancelled'" class="badge badge-secondary">Annulé</span>
                                    </td>
                                </tr>
                            </t>
                        </tbody>
                    </table>
                    
                    <div class="oe_structure"/>
                </div>
            </t>
        </template>
        
        <!-- Rapport des engagements -->
        <record id="action_report_budget_engagement" model="ir.actions.report">
            <field name="name">Rapport Engagements</field>
            <field name="model">budget.engagement.report</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">gestion_budgetaire.report_budget_engagement_document</field>
            <field name="report_file">gestion_budgetaire.report_budget_engagement_document</field>
            <field name="binding_model_id" ref="model_budget_engagement_report"/>
            <field name="binding_type">report</field>
        </record>
        
        <!-- Template pour l'engagement individuel -->
        <template id="report_budget_engagement_individual">
            <t t-call="web.external_layout">
                <t t-foreach="docs" t-as="engagement">
                    <div class="page">
                        <div class="oe_structure"/>
                        
                        <div class="row">
                            <div class="col-12 text-center">
                                <h2>ENGAGEMENT BUDGÉTAIRE</h2>
                                <h3><span t-field="engagement.name"/></h3>
                            </div>
                        </div>
                        
                        <br/>
                        
                        <div class="row">
                            <div class="col-6">
                                <strong>Exercice :</strong> <span t-field="engagement.exercise_id.name"/><br/>
                                <strong>Date :</strong> <span t-field="engagement.date"/><br/>
                                <strong>Poste budgétaire :</strong> <span t-field="engagement.nomenclature_id.complete_code"/> - <span t-field="engagement.nomenclature_id.name"/><br/>
                                <strong>Type d'engagement :</strong> <span t-field="engagement.engagement_type_id.name"/><br/>
                            </div>
                            <div class="col-6">
                                <strong>Tiers :</strong> <span t-field="engagement.partner_id.name"/><br/>
                                <strong>Montant :</strong> <span t-field="engagement.amount" t-options="{'widget': 'monetary', 'display_currency': engagement.currency_id}"/><br/>
                                <strong>État :</strong> 
                                <span t-if="engagement.state == 'draft'" class="badge badge-info">Brouillon</span>
                                <span t-if="engagement.state == 'waiting_validation'" class="badge badge-warning">En attente de validation</span>
                                <span t-if="engagement.state == 'validated'" class="badge badge-success">Validé</span>
                                <span t-if="engagement.state == 'partially_mandated'" class="badge badge-primary">Partiellement mandaté</span>
                                <span t-if="engagement.state == 'fully_mandated'" class="badge badge-primary">Totalement mandaté</span>
                                <span t-if="engagement.state == 'cancelled'" class="badge badge-secondary">Annulé</span>
                            </div>
                        </div>
                        
                        <br/>
                        
                        <div class="row">
                            <div class="col-12">
                                <strong>Description :</strong><br/>
                                <p t-field="engagement.description"/>
                            </div>
                        </div>
                        
                        <t t-if="engagement.reason">
                            <div class="row">
                                <div class="col-12">
                                    <strong>Motif :</strong><br/>
                                    <p t-field="engagement.reason"/>
                                </div>
                            </div>
                        </t>
                        
                        <t t-if="engagement.mandate_ids">
                            <div class="row">
                                <div class="col-12">
                                    <h4>Mandats associés</h4>
                                    <table class="table table-sm table-bordered">
                                        <thead>
                                            <tr class="table-active">
                                                <th>Référence</th>
                                                <th>Date</th>
                                                <th>Type de paiement</th>
                                                <th class="text-right">Montant</th>
                                                <th>État</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <t t-foreach="engagement.mandate_ids" t-as="mandate">
                                                <tr>
                                                    <td><span t-field="mandate.name"/></td>
                                                    <td><span t-field="mandate.date"/></td>
                                                    <td><span t-field="mandate.payment_type_id.name"/></td>
                                                    <td class="text-right">
                                                        <span t-field="mandate.amount" 
                                                              t-options="{'widget': 'monetary', 'display_currency': mandate.currency_id}"/>
                                                    </td>
                                                    <td>
                                                        <span t-if="mandate.state == 'draft'" class="badge badge-info">Brouillon</span>
                                                        <span t-if="mandate.state == 'waiting_validation'" class="badge badge-warning">En attente</span>
                                                        <span t-if="mandate.state == 'validated'" class="badge badge-success">Validé</span>
                                                        <span t-if="mandate.state == 'paid'" class="badge badge-primary">Payé</span>
                                                        <span t-if="mandate.state == 'cancelled'" class="badge badge-secondary">Annulé</span>
                                                    </td>
                                                </tr>
                                            </t>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </t>
                        
                        <div class="oe_structure"/>
                    </div>
                </t>
            </t>
        </template>
        
        <!-- Rapport d'engagement individuel -->
        <record id="action_report_budget_engagement_individual" model="ir.actions.report">
            <field name="name">Engagement Budgétaire</field>
            <field name="model">budget.engagement</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">gestion_budgetaire.report_budget_engagement_individual</field>
            <field name="report_file">gestion_budgetaire.report_budget_engagement_individual</field>
            <field name="binding_model_id" ref="model_budget_engagement"/>
            <field name="binding_type">report</field>
        </record>
        
    </data>
</odoo>
