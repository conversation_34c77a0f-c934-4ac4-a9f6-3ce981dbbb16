# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError


class BudgetAdjustmentWizard(models.TransientModel):
    """Assistant pour créer des ajustements budgétaires"""
    
    _name = 'budget.adjustment.wizard'
    _description = 'Assistant Ajustement Budgétaire'
    
    # Champs de base
    exercise_id = fields.Many2one(
        'budget.exercise',
        string='Exercice budgétaire',
        required=True,
        domain="[('state', 'in', ['open', 'closing'])]",
        default=lambda self: self._get_default_exercise(),
        help='Exercice budgétaire concerné'
    )
    
    adjustment_type = fields.Selection([
        ('transfer', 'Virement de crédits'),
        ('supplementary', 'Budget supplémentaire'),
        ('rectification', 'Budget rectificatif'),
        ('carryover', 'Rattachement de crédits'),
        ('cancellation', 'Annulation de crédits'),
    ], string='Type d\'ajustement', required=True, default='transfer',
       help='Type d\'ajustement budgétaire')
    
    description = fields.Text(
        string='Description',
        required=True,
        help='Description de l\'ajustement'
    )
    
    reason = fields.Text(
        string='Motif',
        required=True,
        help='Motif ou justification de l\'ajustement'
    )
    
    # Lignes d'ajustement
    line_ids = fields.One2many(
        'budget.adjustment.wizard.line',
        'wizard_id',
        string='Lignes d\'ajustement',
        help='Détail des ajustements par poste budgétaire'
    )
    
    # Totaux calculés
    total_debit = fields.Monetary(
        string='Total débit',
        compute='_compute_totals',
        currency_field='currency_id',
        help='Total des montants en débit'
    )
    
    total_credit = fields.Monetary(
        string='Total crédit',
        compute='_compute_totals',
        currency_field='currency_id',
        help='Total des montants en crédit'
    )
    
    balance = fields.Monetary(
        string='Solde',
        compute='_compute_totals',
        currency_field='currency_id',
        help='Différence entre débit et crédit'
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Devise',
        related='exercise_id.currency_id',
        readonly=True
    )
    
    # Méthodes par défaut
    def _get_default_exercise(self):
        """Obtenir l'exercice budgétaire par défaut"""
        return self.env['budget.exercise'].get_current_exercise()
    
    # Méthodes de calcul
    @api.depends('line_ids.amount', 'line_ids.movement_type')
    def _compute_totals(self):
        """Calculer les totaux"""
        for wizard in self:
            debit_lines = wizard.line_ids.filtered(lambda l: l.movement_type == 'debit')
            credit_lines = wizard.line_ids.filtered(lambda l: l.movement_type == 'credit')
            
            wizard.total_debit = sum(debit_lines.mapped('amount'))
            wizard.total_credit = sum(credit_lines.mapped('amount'))
            wizard.balance = wizard.total_debit - wizard.total_credit
    
    # Actions
    def action_create_adjustment(self):
        """Créer l'ajustement budgétaire"""
        self.ensure_one()
        
        # Vérifications
        if not self.line_ids:
            raise ValidationError(_('Vous devez ajouter au moins une ligne d\'ajustement.'))
        
        if self.adjustment_type == 'transfer' and abs(self.balance) > 0.01:
            raise ValidationError(_('Un virement de crédits doit être équilibré (débit = crédit).'))
        
        # Créer l'ajustement
        adjustment_vals = {
            'exercise_id': self.exercise_id.id,
            'adjustment_type': self.adjustment_type,
            'description': self.description,
            'reason': self.reason,
        }
        
        adjustment = self.env['budget.adjustment'].create(adjustment_vals)
        
        # Créer les lignes
        for line in self.line_ids:
            line_vals = {
                'adjustment_id': adjustment.id,
                'nomenclature_id': line.nomenclature_id.id,
                'movement_type': line.movement_type,
                'amount': line.amount,
                'description': line.description,
            }
            self.env['budget.adjustment.line'].create(line_vals)
        
        # Retourner l'action pour voir l'ajustement créé
        return {
            'name': _('Ajustement Budgétaire'),
            'type': 'ir.actions.act_window',
            'res_model': 'budget.adjustment',
            'res_id': adjustment.id,
            'view_mode': 'form',
            'target': 'current',
        }


class BudgetAdjustmentWizardLine(models.TransientModel):
    """Lignes de l'assistant d'ajustement budgétaire"""
    
    _name = 'budget.adjustment.wizard.line'
    _description = 'Ligne Assistant Ajustement Budgétaire'
    
    wizard_id = fields.Many2one(
        'budget.adjustment.wizard',
        string='Assistant',
        required=True,
        ondelete='cascade'
    )
    
    nomenclature_id = fields.Many2one(
        'budget.nomenclature',
        string='Poste budgétaire',
        required=True,
        domain="[('is_analytical', '=', True)]",
        help='Poste budgétaire concerné'
    )
    
    movement_type = fields.Selection([
        ('debit', 'Débit (Diminution)'),
        ('credit', 'Crédit (Augmentation)'),
    ], string='Type de mouvement', required=True,
       help='Type de mouvement budgétaire')
    
    amount = fields.Monetary(
        string='Montant',
        currency_field='currency_id',
        required=True,
        help='Montant de l\'ajustement'
    )
    
    description = fields.Text(
        string='Description',
        help='Description de cette ligne d\'ajustement'
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Devise',
        related='wizard_id.currency_id',
        readonly=True
    )
    
    # Contraintes
    @api.constrains('amount')
    def _check_amount_positive(self):
        """Vérifier que le montant est positif"""
        for record in self:
            if record.amount <= 0:
                raise ValidationError(_('Le montant doit être positif.'))


class BudgetImportWizard(models.TransientModel):
    """Assistant pour importer des données budgétaires"""
    
    _name = 'budget.import.wizard'
    _description = 'Assistant Import Budgétaire'
    
    exercise_id = fields.Many2one(
        'budget.exercise',
        string='Exercice budgétaire',
        required=True,
        domain="[('state', 'in', ['draft', 'open'])]",
        help='Exercice budgétaire de destination'
    )
    
    import_type = fields.Selection([
        ('credits', 'Crédits budgétaires'),
        ('nomenclature', 'Nomenclature budgétaire'),
    ], string='Type d\'import', required=True, default='credits',
       help='Type de données à importer')
    
    file_data = fields.Binary(
        string='Fichier',
        required=True,
        help='Fichier CSV ou Excel à importer'
    )
    
    file_name = fields.Char(
        string='Nom du fichier',
        help='Nom du fichier importé'
    )
    
    def action_import_data(self):
        """Importer les données"""
        self.ensure_one()
        
        if not self.file_data:
            raise ValidationError(_('Veuillez sélectionner un fichier à importer.'))
        
        # TODO: Implémenter l'import selon le type
        if self.import_type == 'credits':
            return self._import_credits()
        elif self.import_type == 'nomenclature':
            return self._import_nomenclature()
    
    def _import_credits(self):
        """Importer les crédits budgétaires"""
        # TODO: Implémenter l'import des crédits depuis CSV/Excel
        raise UserError(_('Fonctionnalité d\'import des crédits en cours de développement.'))
    
    def _import_nomenclature(self):
        """Importer la nomenclature budgétaire"""
        # TODO: Implémenter l'import de la nomenclature depuis CSV/Excel
        raise UserError(_('Fonctionnalité d\'import de la nomenclature en cours de développement.'))
