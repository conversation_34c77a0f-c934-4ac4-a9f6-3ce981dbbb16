# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
import logging

_logger = logging.getLogger(__name__)


class BudgetCredit(models.Model):
    """Modèle pour la gestion des crédits budgétaires"""
    
    _name = 'budget.credit'
    _description = 'Crédit <PERSON>'
    _order = 'exercise_id desc, nomenclature_id'
    _check_company_auto = True
    
    # Champs de base
    name = fields.Char(
        string='Référence',
        compute='_compute_name',
        store=True,
        help='Référence automatique du crédit'
    )
    
    exercise_id = fields.Many2one(
        'budget.exercise',
        string='Exercice budgétaire',
        required=True,
        ondelete='cascade',
        help='Exercice budgétaire concerné'
    )
    
    nomenclature_id = fields.Many2one(
        'budget.nomenclature',
        string='Poste budgétaire',
        required=True,
        domain="[('is_analytical', '=', True), ('company_id', '=', company_id)]",
        help='Poste budgétaire concerné'
    )
    
    company_id = fields.Many2one(
        'res.company',
        string='Société',
        related='exercise_id.company_id',
        store=True,
        readonly=True
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Devise',
        related='company_id.currency_id',
        store=True,
        readonly=True
    )
    
    # Montants
    amount_initial = fields.Monetary(
        string='Crédit initial',
        currency_field='currency_id',
        default=0.0,
        help='Montant du crédit initial alloué'
    )
    
    amount_adjustments = fields.Monetary(
        string='Ajustements',
        compute='_compute_amounts',
        store=True,
        currency_field='currency_id',
        help='Total des ajustements (virements, budgets supplémentaires, etc.)'
    )
    
    amount_voted = fields.Monetary(
        string='Crédit voté',
        compute='_compute_amounts',
        store=True,
        currency_field='currency_id',
        help='Crédit après ajustements (Initial + Ajustements)'
    )
    
    amount_engaged = fields.Monetary(
        string='Montant engagé',
        compute='_compute_amounts',
        store=True,
        currency_field='currency_id',
        help='Total des engagements validés'
    )
    
    amount_mandated = fields.Monetary(
        string='Montant mandaté',
        compute='_compute_amounts',
        store=True,
        currency_field='currency_id',
        help='Total des mandatements payés'
    )
    
    amount_available = fields.Monetary(
        string='Crédit disponible',
        compute='_compute_amounts',
        store=True,
        currency_field='currency_id',
        help='Crédit disponible (Voté - Engagé)'
    )
    
    amount_to_pay = fields.Monetary(
        string='Reste à payer',
        compute='_compute_amounts',
        store=True,
        currency_field='currency_id',
        help='Montant engagé non encore mandaté'
    )
    
    # Pourcentages
    consumption_rate = fields.Float(
        string='Taux de consommation (%)',
        compute='_compute_rates',
        store=True,
        help='Pourcentage de consommation (Engagé / Voté * 100)'
    )
    
    payment_rate = fields.Float(
        string='Taux de paiement (%)',
        compute='_compute_rates',
        store=True,
        help='Pourcentage de paiement (Mandaté / Engagé * 100)'
    )
    
    # État et contrôles
    state = fields.Selection([
        ('draft', 'Brouillon'),
        ('validated', 'Validé'),
        ('blocked', 'Bloqué'),
    ], string='État', default='draft', required=True, tracking=True,
       help='État du crédit budgétaire')
    
    is_blocked = fields.Boolean(
        string='Bloqué',
        default=False,
        help='Crédit bloqué pour nouveaux engagements'
    )
    
    blocking_reason = fields.Text(
        string='Motif de blocage',
        help='Raison du blocage du crédit'
    )
    
    # Seuils d'alerte
    alert_threshold = fields.Float(
        string='Seuil d\'alerte (%)',
        default=80.0,
        help='Seuil de consommation déclenchant une alerte'
    )
    
    blocking_threshold = fields.Float(
        string='Seuil de blocage (%)',
        default=100.0,
        help='Seuil de consommation déclenchant un blocage automatique'
    )
    
    # Relations
    adjustment_ids = fields.One2many(
        'budget.adjustment',
        'credit_id',
        string='Ajustements',
        help='Ajustements appliqués à ce crédit'
    )
    
    engagement_ids = fields.One2many(
        'budget.engagement',
        'credit_id',
        string='Engagements',
        help='Engagements imputés sur ce crédit'
    )
    
    # Champs informatifs
    notes = fields.Text(
        string='Notes',
        help='Notes et commentaires sur ce crédit'
    )
    
    # Contraintes
    @api.constrains('amount_initial')
    def _check_amount_initial(self):
        """Vérifier que le montant initial est positif"""
        for record in self:
            if record.amount_initial < 0:
                raise ValidationError(
                    _('Le montant initial ne peut pas être négatif.')
                )
    
    @api.constrains('exercise_id', 'nomenclature_id')
    def _check_unique_credit(self):
        """Vérifier l'unicité du crédit par exercice et poste"""
        for record in self:
            existing = self.search([
                ('exercise_id', '=', record.exercise_id.id),
                ('nomenclature_id', '=', record.nomenclature_id.id),
                ('id', '!=', record.id)
            ])
            if existing:
                raise ValidationError(
                    _('Un crédit existe déjà pour ce poste budgétaire sur cet exercice.')
                )
    
    @api.constrains('nomenclature_id')
    def _check_analytical_position(self):
        """Vérifier que le poste budgétaire est analytique"""
        for record in self:
            if not record.nomenclature_id.is_analytical:
                raise ValidationError(
                    _('Seuls les postes analytiques peuvent recevoir des crédits.')
                )
    
    # Méthodes de calcul
    @api.depends('exercise_id', 'nomenclature_id')
    def _compute_name(self):
        """Calculer la référence automatique"""
        for record in self:
            if record.exercise_id and record.nomenclature_id:
                record.name = f"{record.exercise_id.code}/{record.nomenclature_id.complete_code}"
            else:
                record.name = _('Nouveau crédit')
    
    @api.depends('amount_initial', 'adjustment_ids.amount', 'adjustment_ids.state',
                 'engagement_ids.amount', 'engagement_ids.state',
                 'engagement_ids.mandate_ids.amount', 'engagement_ids.mandate_ids.state')
    def _compute_amounts(self):
        """Calculer les montants dérivés"""
        for record in self:
            # Ajustements validés
            record.amount_adjustments = sum(
                adj.amount for adj in record.adjustment_ids
                if adj.state == 'validated'
            )
            
            # Crédit voté
            record.amount_voted = record.amount_initial + record.amount_adjustments
            
            # Engagements validés
            record.amount_engaged = sum(
                eng.amount for eng in record.engagement_ids
                if eng.state == 'validated'
            )
            
            # Mandatements payés
            record.amount_mandated = sum(
                mandate.amount for mandate in record.engagement_ids.mapped('mandate_ids')
                if mandate.state == 'paid'
            )
            
            # Crédit disponible
            record.amount_available = record.amount_voted - record.amount_engaged
            
            # Reste à payer
            record.amount_to_pay = record.amount_engaged - record.amount_mandated
    
    @api.depends('amount_voted', 'amount_engaged', 'amount_mandated')
    def _compute_rates(self):
        """Calculer les taux de consommation et de paiement"""
        for record in self:
            # Taux de consommation
            if record.amount_voted:
                record.consumption_rate = (record.amount_engaged / record.amount_voted) * 100
            else:
                record.consumption_rate = 0.0
            
            # Taux de paiement
            if record.amount_engaged:
                record.payment_rate = (record.amount_mandated / record.amount_engaged) * 100
            else:
                record.payment_rate = 0.0
    
    # Méthodes d'action
    def action_validate(self):
        """Valider le crédit budgétaire"""
        for record in self:
            if record.state != 'draft':
                raise UserError(_('Seul un crédit en brouillon peut être validé.'))
            record.state = 'validated'
            _logger.info('Crédit budgétaire %s validé', record.name)
    
    def action_block(self):
        """Bloquer le crédit budgétaire"""
        for record in self:
            record.is_blocked = True
            record.state = 'blocked'
            _logger.info('Crédit budgétaire %s bloqué', record.name)
    
    def action_unblock(self):
        """Débloquer le crédit budgétaire"""
        for record in self:
            record.is_blocked = False
            record.blocking_reason = False
            record.state = 'validated'
            _logger.info('Crédit budgétaire %s débloqué', record.name)
    
    def check_availability(self, amount):
        """Vérifier la disponibilité d'un montant"""
        self.ensure_one()
        if self.is_blocked:
            raise UserError(
                _('Le crédit %s est bloqué : %s') % (self.name, self.blocking_reason or '')
            )
        
        if amount > self.amount_available:
            raise UserError(
                _('Crédit insuffisant sur %s. Disponible: %s, Demandé: %s') % 
                (self.name, self.amount_available, amount)
            )
        
        # Vérifier les seuils
        new_consumption_rate = ((self.amount_engaged + amount) / self.amount_voted) * 100 if self.amount_voted else 0
        
        if new_consumption_rate >= self.blocking_threshold:
            raise UserError(
                _('Le seuil de blocage (%s%%) serait dépassé sur %s') % 
                (self.blocking_threshold, self.name)
            )
        
        return True
    
    def get_alert_status(self):
        """Obtenir le statut d'alerte du crédit"""
        self.ensure_one()
        if self.consumption_rate >= self.blocking_threshold:
            return 'danger'
        elif self.consumption_rate >= self.alert_threshold:
            return 'warning'
        else:
            return 'success'
    
    # Actions de vue
    def action_view_adjustments(self):
        """Voir les ajustements de ce crédit"""
        self.ensure_one()
        return {
            'name': _('Ajustements budgétaires'),
            'type': 'ir.actions.act_window',
            'res_model': 'budget.adjustment',
            'view_mode': 'tree,form',
            'domain': [('credit_id', '=', self.id)],
            'context': {'default_credit_id': self.id},
        }
    
    def action_view_engagements(self):
        """Voir les engagements de ce crédit"""
        self.ensure_one()
        return {
            'name': _('Engagements'),
            'type': 'ir.actions.act_window',
            'res_model': 'budget.engagement',
            'view_mode': 'tree,form',
            'domain': [('credit_id', '=', self.id)],
            'context': {'default_credit_id': self.id},
        }
