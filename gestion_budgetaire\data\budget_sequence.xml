<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Séquence pour les engagements budgétaires -->
        <record id="seq_budget_engagement" model="ir.sequence">
            <field name="name">Engagement Budgétaire</field>
            <field name="code">budget.engagement</field>
            <field name="prefix">ENG/%(year)s/</field>
            <field name="suffix"></field>
            <field name="padding">5</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="use_date_range">True</field>
            <field name="company_id" eval="False"/>
        </record>
        
        <!-- Séquence pour les mandats -->
        <record id="seq_budget_mandate" model="ir.sequence">
            <field name="name">Mandat de Paiement</field>
            <field name="code">budget.mandate</field>
            <field name="prefix">MAN/%(year)s/</field>
            <field name="suffix"></field>
            <field name="padding">5</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="use_date_range">True</field>
            <field name="company_id" eval="False"/>
        </record>
        
        <!-- Séquence pour les ajustements - Virements -->
        <record id="seq_budget_adjustment_transfer" model="ir.sequence">
            <field name="name">Virement de Crédits</field>
            <field name="code">budget.adjustment.transfer</field>
            <field name="prefix">VIR/%(year)s/</field>
            <field name="suffix"></field>
            <field name="padding">4</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="use_date_range">True</field>
            <field name="company_id" eval="False"/>
        </record>
        
        <!-- Séquence pour les ajustements - Budgets supplémentaires -->
        <record id="seq_budget_adjustment_supplementary" model="ir.sequence">
            <field name="name">Budget Supplémentaire</field>
            <field name="code">budget.adjustment.supplementary</field>
            <field name="prefix">BS/%(year)s/</field>
            <field name="suffix"></field>
            <field name="padding">4</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="use_date_range">True</field>
            <field name="company_id" eval="False"/>
        </record>
        
        <!-- Séquence pour les ajustements - Budgets rectificatifs -->
        <record id="seq_budget_adjustment_rectification" model="ir.sequence">
            <field name="name">Budget Rectificatif</field>
            <field name="code">budget.adjustment.rectification</field>
            <field name="prefix">BR/%(year)s/</field>
            <field name="suffix"></field>
            <field name="padding">4</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="use_date_range">True</field>
            <field name="company_id" eval="False"/>
        </record>
        
        <!-- Séquence pour les ajustements - Rattachements -->
        <record id="seq_budget_adjustment_carryover" model="ir.sequence">
            <field name="name">Rattachement de Crédits</field>
            <field name="code">budget.adjustment.carryover</field>
            <field name="prefix">RAT/%(year)s/</field>
            <field name="suffix"></field>
            <field name="padding">4</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="use_date_range">True</field>
            <field name="company_id" eval="False"/>
        </record>
        
        <!-- Séquence pour les ajustements - Annulations -->
        <record id="seq_budget_adjustment_cancellation" model="ir.sequence">
            <field name="name">Annulation de Crédits</field>
            <field name="code">budget.adjustment.cancellation</field>
            <field name="prefix">ANN/%(year)s/</field>
            <field name="suffix"></field>
            <field name="padding">4</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="use_date_range">True</field>
            <field name="company_id" eval="False"/>
        </record>
        
        <!-- Séquence pour les titres de perception -->
        <record id="seq_budget_perception_title" model="ir.sequence">
            <field name="name">Titre de Perception</field>
            <field name="code">budget.perception.title</field>
            <field name="prefix">TP/%(year)s/</field>
            <field name="suffix"></field>
            <field name="padding">5</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="use_date_range">True</field>
            <field name="company_id" eval="False"/>
        </record>
        
        <!-- Séquence pour les encaissements -->
        <record id="seq_budget_perception_collection" model="ir.sequence">
            <field name="name">Encaissement</field>
            <field name="code">budget.perception.collection</field>
            <field name="prefix">ENC/%(year)s/</field>
            <field name="suffix"></field>
            <field name="padding">5</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="use_date_range">True</field>
            <field name="company_id" eval="False"/>
        </record>
        
    </data>
</odoo>
