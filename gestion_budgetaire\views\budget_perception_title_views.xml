<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Vue liste des titres de perception -->
        <record id="view_budget_perception_title_tree" model="ir.ui.view">
            <field name="name">budget.perception.title.tree</field>
            <field name="model">budget.perception.title</field>
            <field name="arch" type="xml">
                <tree string="Titres de Perception" decoration-info="state=='draft'"
                      decoration-warning="state=='issued'" decoration-success="state=='fully_collected'"
                      decoration-primary="state=='partially_collected'" decoration-muted="state=='cancelled'">
                    <field name="name"/>
                    <field name="date"/>
                    <field name="exercise_id"/>
                    <field name="nomenclature_id"/>
                    <field name="partner_id"/>
                    <field name="amount" widget="monetary"/>
                    <field name="amount_collected" widget="monetary"/>
                    <field name="amount_remaining" widget="monetary"/>
                    <field name="due_date"/>
                    <field name="state" widget="badge"
                           decoration-info="state=='draft'"
                           decoration-warning="state=='issued'"
                           decoration-primary="state=='partially_collected'"
                           decoration-success="state=='fully_collected'"
                           decoration-muted="state=='cancelled'"/>
                    <field name="user_id" widget="many2one_avatar_user"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!-- Vue formulaire des titres de perception -->
        <record id="view_budget_perception_title_form" model="ir.ui.view">
            <field name="name">budget.perception.title.form</field>
            <field name="model">budget.perception.title</field>
            <field name="arch" type="xml">
                <form string="Titre de Perception">
                    <header>
                        <button name="action_issue" string="Émettre" type="object"
                                class="oe_highlight" invisible="state != 'draft'"
                                groups="gestion_budgetaire.group_budget_agent"/>
                        <button name="action_create_collection" string="Créer Encaissement" type="object"
                                invisible="state != 'issued' or amount_remaining &lt;= 0"
                                groups="gestion_budgetaire.group_budget_agent"/>
                        <button name="action_cancel" string="Annuler" type="object"
                                invisible="state in ('fully_collected', 'cancelled')"
                                groups="gestion_budgetaire.group_budget_controller"/>
                        <button name="action_reset_to_draft" string="Remettre en brouillon" type="object"
                                invisible="state not in ('issued', 'cancelled')"
                                groups="gestion_budgetaire.group_budget_agent"/>
                        <field name="state" widget="statusbar"
                               statusbar_visible="draft,issued,partially_collected,fully_collected"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_collections" type="object"
                                    class="oe_stat_button" icon="fa-money">
                                <div class="o_stat_info">
                                    <span class="o_stat_text">Encaissements</span>
                                </div>
                            </button>
                        </div>

                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>

                        <group>
                            <group>
                                <field name="date" readonly="state in ('issued', 'partially_collected', 'fully_collected', 'cancelled')"/>
                                <field name="due_date" readonly="state in ('partially_collected', 'fully_collected', 'cancelled')"/>
                                <field name="exercise_id" readonly="state != 'draft'"/>
                                <field name="nomenclature_id" readonly="state != 'draft'"/>
                            </group>
                            <group>
                                <field name="partner_id" readonly="state in ('issued', 'partially_collected', 'fully_collected', 'cancelled')"/>
                                <field name="amount" widget="monetary" readonly="state in ('issued', 'partially_collected', 'fully_collected', 'cancelled')"/>
                                <field name="amount_collected" widget="monetary"/>
                                <field name="amount_remaining" widget="monetary"/>
                                <field name="user_id"/>
                                <field name="currency_id" invisible="1"/>
                                <field name="company_id" invisible="not context.get('show_company', False)"/>
                            </group>
                        </group>

                        <group string="Description">
                            <field name="description" nolabel="1" placeholder="Description du titre de perception"
                                   readonly="state in ('issued', 'partially_collected', 'fully_collected', 'cancelled')"/>
                        </group>

                        <group string="Motif" invisible="not reason">
                            <field name="reason" nolabel="1" placeholder="Motif ou justification"
                                   readonly="state in ('issued', 'partially_collected', 'fully_collected', 'cancelled')"/>
                        </group>

                        <group string="Documents Sources" invisible="not invoice_id and not sale_order_id">
                            <group>
                                <field name="invoice_id" readonly="1"/>
                            </group>
                            <group>
                                <field name="sale_order_id" readonly="1"/>
                            </group>
                        </group>

                        <group string="Suivi" invisible="state == 'draft'">
                            <group>
                                <field name="issued_by" readonly="1"/>
                                <field name="issued_date" readonly="1"/>
                            </group>
                        </group>

                        <notebook>
                            <page string="Encaissements" name="collections">
                                <field name="collection_ids" readonly="state == 'cancelled'">
                                    <tree>
                                        <field name="name"/>
                                        <field name="date"/>
                                        <field name="amount" widget="monetary"/>
                                        <field name="state" widget="badge"/>
                                        <field name="currency_id" invisible="1"/>
                                    </tree>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Vue kanban des titres de perception -->
        <record id="view_budget_perception_title_kanban" model="ir.ui.view">
            <field name="name">budget.perception.title.kanban</field>
            <field name="model">budget.perception.title</field>
            <field name="arch" type="xml">
                <kanban class="o_kanban_mobile" default_group_by="state">
                    <field name="name"/>
                    <field name="date"/>
                    <field name="partner_id"/>
                    <field name="amount"/>
                    <field name="amount_collected"/>
                    <field name="amount_remaining"/>
                    <field name="state"/>
                    <field name="currency_id"/>
                    <field name="user_id"/>
                    <templates>
                        <t t-name="kanban-box">
                            <div class="oe_kanban_card oe_kanban_global_click">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="date"/> - <field name="partner_id"/>
                                        </small>
                                    </div>
                                    <span class="o_kanban_record_top_right">
                                        <field name="user_id" widget="many2one_avatar_user"/>
                                    </span>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-6">
                                            <span>À percevoir:</span><br/>
                                            <strong><field name="amount" widget="monetary"/></strong>
                                        </div>
                                        <div class="col-6">
                                            <span>Encaissé:</span><br/>
                                            <strong><field name="amount_collected" widget="monetary"/></strong>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <span>Reste:</span><br/>
                                            <strong><field name="amount_remaining" widget="monetary"/></strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </t>
                    </templates>
                </kanban>
            </field>
        </record>

        <!-- Vue recherche des titres de perception -->
        <record id="view_budget_perception_title_search" model="ir.ui.view">
            <field name="name">budget.perception.title.search</field>
            <field name="model">budget.perception.title</field>
            <field name="arch" type="xml">
                <search string="Rechercher Titres de Perception">
                    <field name="name"/>
                    <field name="exercise_id"/>
                    <field name="nomenclature_id"/>
                    <field name="partner_id"/>
                    <field name="user_id"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                    <separator/>
                    <filter string="Mes titres" name="my_titles" domain="[('user_id', '=', uid)]"/>
                    <separator/>
                    <filter string="Brouillon" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Émis" name="issued" domain="[('state', '=', 'issued')]"/>
                    <filter string="Partiellement recouvré" name="partially_collected" domain="[('state', '=', 'partially_collected')]"/>
                    <filter string="Totalement recouvré" name="fully_collected" domain="[('state', '=', 'fully_collected')]"/>
                    <filter string="Annulé" name="cancelled" domain="[('state', '=', 'cancelled')]"/>
                    <separator/>
                    <filter string="À recouvrer" name="to_collect" domain="[('state', 'in', ['issued', 'partially_collected']), ('amount_remaining', '>', 0)]"/>
                    <filter string="Échus" name="overdue" domain="[('due_date', '&lt;', context_today()), ('state', 'in', ['issued', 'partially_collected'])]"/>
                    <separator/>
                    <filter string="Ce mois" name="this_month"
                            domain="[('date', '&gt;=', context_today().strftime('%Y-%m-01')), ('date', '&lt;', (context_today() + relativedelta(months=1)).strftime('%Y-%m-01'))]"/>
                    <filter string="Exercice actuel" name="current_exercise"
                            domain="[('exercise_id.state', '=', 'open')]"/>
                    <separator/>
                    <group expand="0" string="Grouper par">
                        <filter string="État" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Exercice" name="group_exercise" context="{'group_by': 'exercise_id'}"/>
                        <filter string="Poste budgétaire" name="group_nomenclature" context="{'group_by': 'nomenclature_id'}"/>
                        <filter string="Débiteur" name="group_partner" context="{'group_by': 'partner_id'}"/>
                        <filter string="Responsable" name="group_user" context="{'group_by': 'user_id'}"/>
                        <filter string="Date" name="group_date" context="{'group_by': 'date:month'}"/>
                        <filter string="Société" name="group_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Action pour les titres de perception -->
        <record id="action_budget_perception_title" model="ir.actions.act_window">
            <field name="name">Titres de Perception</field>
            <field name="res_model">budget.perception.title</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_perception_title_search"/>
            <field name="context">{'search_default_current_exercise': 1}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Créer un nouveau titre de perception
                </p>
                <p>
                    Les titres de perception représentent les créances
                    à recouvrer auprès des débiteurs.
                </p>
                <p>
                    Ils peuvent être créés manuellement ou automatiquement
                    depuis les factures clients.
                </p>
            </field>
        </record>

        <!-- Action pour les titres à recouvrer -->
        <record id="action_budget_perception_title_to_collect" model="ir.actions.act_window">
            <field name="name">Titres à Recouvrer</field>
            <field name="res_model">budget.perception.title</field>
            <field name="view_mode">tree,kanban,form</field>
            <field name="search_view_id" ref="view_budget_perception_title_search"/>
            <field name="domain">[('state', 'in', ['issued', 'partially_collected']), ('amount_remaining', '>', 0)]</field>
            <field name="context">{'search_default_to_collect': 1}</field>
        </record>

        <!-- Vue liste des encaissements -->
        <record id="view_budget_perception_collection_tree" model="ir.ui.view">
            <field name="name">budget.perception.collection.tree</field>
            <field name="model">budget.perception.collection</field>
            <field name="arch" type="xml">
                <tree string="Encaissements" decoration-info="state=='draft'"
                      decoration-success="state=='collected'" decoration-muted="state=='cancelled'">
                    <field name="name"/>
                    <field name="date"/>
                    <field name="title_id"/>
                    <field name="partner_id"/>
                    <field name="amount" widget="monetary"/>
                    <field name="state" widget="badge"
                           decoration-info="state=='draft'"
                           decoration-success="state=='collected'"
                           decoration-muted="state=='cancelled'"/>
                    <field name="currency_id" invisible="1"/>
                    <field name="company_id" groups="base.group_multi_company"/>
                </tree>
            </field>
        </record>

        <!-- Vue formulaire des encaissements -->
        <record id="view_budget_perception_collection_form" model="ir.ui.view">
            <field name="name">budget.perception.collection.form</field>
            <field name="model">budget.perception.collection</field>
            <field name="arch" type="xml">
                <form string="Encaissement">
                    <header>
                        <button name="action_collect" string="Marquer comme encaissé" type="object"
                                class="oe_highlight" invisible="state != 'draft'"
                                groups="gestion_budgetaire.group_budget_agent"/>
                        <button name="action_cancel" string="Annuler" type="object"
                                invisible="state in ('collected', 'cancelled')"
                                groups="gestion_budgetaire.group_budget_controller"/>
                        <button name="action_reset_to_draft" string="Remettre en brouillon" type="object"
                                invisible="state != 'cancelled'"
                                groups="gestion_budgetaire.group_budget_agent"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,collected"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>

                        <group>
                            <group>
                                <field name="date" readonly="state in ('collected', 'cancelled')"/>
                                <field name="title_id" readonly="state != 'draft'"/>
                                <field name="exercise_id" readonly="1"/>
                                <field name="nomenclature_id" readonly="1"/>
                            </group>
                            <group>
                                <field name="partner_id" readonly="1"/>
                                <field name="amount" widget="monetary" readonly="state in ('collected', 'cancelled')"/>
                                <field name="currency_id" invisible="1"/>
                                <field name="company_id" invisible="not context.get('show_company', False)"/>
                            </group>
                        </group>

                        <group string="Description">
                            <field name="description" nolabel="1" placeholder="Description de l'encaissement"
                                   readonly="state in ('collected', 'cancelled')"/>
                        </group>

                        <group string="Paiement Lié" invisible="not payment_id">
                            <field name="payment_id" readonly="1"/>
                        </group>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids"/>
                        <field name="activity_ids"/>
                        <field name="message_ids"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Action pour les encaissements -->
        <record id="action_budget_perception_collection" model="ir.actions.act_window">
            <field name="name">Encaissements</field>
            <field name="res_model">budget.perception.collection</field>
            <field name="view_mode">tree,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Créer un nouvel encaissement
                </p>
                <p>
                    Les encaissements représentent les paiements reçus
                    pour les titres de perception émis.
                </p>
            </field>
        </record>

    </data>
</odoo>
