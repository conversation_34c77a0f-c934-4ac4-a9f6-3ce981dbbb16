# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Odoo specific
*.pyc
*.pyo
*.orig
*.rej
*~
.DS_Store
._.DS_Store
.coverage
.installed.cfg
.mr.developer.cfg
.project
.pydevproject
.settings
.idea/
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Log files
*.log

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config.conf
local.conf

# IDE files
.vscode/launch.json
.vscode/tasks.json
*.sublime-project
*.sublime-workspace

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Archives
*.zip
*.tar.gz
*.rar
*.7z

# Odoo filestore (if accidentally included)
filestore/

# Test databases
test_*.db
test_*.sql
