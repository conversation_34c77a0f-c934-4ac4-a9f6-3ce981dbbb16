<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Vue formulaire de l'assistant d'import -->
        <record id="view_budget_import_wizard_form" model="ir.ui.view">
            <field name="name">budget.import.wizard.form</field>
            <field name="model">budget.import.wizard</field>
            <field name="arch" type="xml">
                <form string="Assistant Import Budgétaire">
                    <sheet>
                        <div class="oe_title">
                            <h1>Importer des Données Budgétaires</h1>
                        </div>
                        
                        <group>
                            <group>
                                <field name="exercise_id"/>
                                <field name="import_type"/>
                            </group>
                            <group>
                                <field name="file_data" filename="file_name"/>
                                <field name="file_name" invisible="1"/>
                            </group>
                        </group>
                        
                        <div class="alert alert-info" role="alert">
                            <strong>Format attendu :</strong>
                            <ul>
                                <li><strong>Crédits budgétaires :</strong> Code poste, <PERSON><PERSON><PERSON>, Montant initial</li>
                                <li><strong>Nomenclature :</strong> <PERSON>, Libellé, Code parent, Type, Nature</li>
                            </ul>
                            <p>Formats supportés : CSV (séparateur ;) et Excel (.xlsx)</p>
                        </div>
                    </sheet>
                    <footer>
                        <button name="action_import_data" string="Importer" 
                                type="object" class="oe_highlight"/>
                        <button string="Annuler" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>
        
        <!-- Action pour l'assistant d'import -->
        <record id="action_budget_import_wizard" model="ir.actions.act_window">
            <field name="name">Assistant Import Budgétaire</field>
            <field name="res_model">budget.import.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>
        
    </data>
</odoo>
