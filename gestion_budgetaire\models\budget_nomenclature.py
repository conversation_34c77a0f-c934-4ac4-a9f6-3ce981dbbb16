# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
import logging

_logger = logging.getLogger(__name__)


class BudgetNomenclature(models.Model):
    """Modèle pour la nomenclature budgétaire hiérarchique"""
    
    _name = 'budget.nomenclature'
    _description = 'Nomenclature Budgétaire'
    _order = 'sequence, code, name'
    _parent_store = True
    _check_company_auto = True
    
    # Champs de base
    name = fields.Char(
        string='Libellé',
        required=True,
        translate=True,
        help='Libellé du poste budgétaire'
    )
    
    code = fields.Char(
        string='Code',
        required=True,
        size=20,
        help='Code unique du poste budgétaire'
    )
    
    complete_code = fields.Char(
        string='Code complet',
        compute='_compute_complete_code',
        store=True,
        help='Code complet incluant la hiérarchie'
    )
    
    sequence = fields.Integer(
        string='Séquence',
        default=10,
        help='Ordre d\'affichage'
    )
    
    active = fields.Boolean(
        string='Actif',
        default=True,
        help='Décocher pour archiver le poste budgétaire'
    )
    
    # Hiérarchie
    parent_id = fields.Many2one(
        'budget.nomenclature',
        string='Poste parent',
        ondelete='cascade',
        help='Poste budgétaire parent dans la hiérarchie'
    )
    
    parent_path = fields.Char(index=True)
    
    child_ids = fields.One2many(
        'budget.nomenclature',
        'parent_id',
        string='Postes enfants',
        help='Postes budgétaires enfants'
    )
    
    level = fields.Integer(
        string='Niveau',
        compute='_compute_level',
        store=True,
        help='Niveau dans la hiérarchie (0 = racine)'
    )
    
    # Type et nature
    budget_type = fields.Selection([
        ('expense', 'Dépense'),
        ('revenue', 'Recette'),
    ], string='Type budgétaire', required=True, default='expense',
       help='Type de budget : dépense ou recette')
    
    nature = fields.Selection([
        ('section', 'Section'),
        ('chapter', 'Chapitre'),
        ('article', 'Article'),
        ('paragraph', 'Paragraphe'),
        ('line', 'Ligne'),
    ], string='Nature', required=True, default='line',
       help='Nature du poste dans la nomenclature')
    
    is_analytical = fields.Boolean(
        string='Poste analytique',
        default=False,
        help='Cocher si ce poste peut recevoir des imputations directes'
    )
    
    # Société
    company_id = fields.Many2one(
        'res.company',
        string='Société',
        required=True,
        default=lambda self: self.env.company,
        help='Société concernée'
    )
    
    # Liens comptables
    account_analytic_id = fields.Many2one(
        'account.analytic.account',
        string='Compte analytique',
        help='Compte analytique lié pour l\'intégration comptable'
    )
    
    account_ids = fields.Many2many(
        'account.account',
        string='Comptes généraux',
        help='Comptes généraux liés à ce poste budgétaire'
    )
    
    # Description
    description = fields.Text(
        string='Description',
        translate=True,
        help='Description détaillée du poste budgétaire'
    )
    
    # Champs calculés
    has_children = fields.Boolean(
        string='A des enfants',
        compute='_compute_has_children',
        help='Indique si ce poste a des enfants'
    )
    
    full_name = fields.Char(
        string='Nom complet',
        compute='_compute_full_name',
        store=True,
        help='Nom complet avec hiérarchie'
    )
    
    # Relations avec les crédits
    credit_ids = fields.One2many(
        'budget.credit',
        'nomenclature_id',
        string='Crédits budgétaires',
        help='Crédits alloués à ce poste'
    )
    
    # Contraintes
    @api.constrains('code', 'parent_id', 'company_id')
    def _check_unique_code(self):
        """Vérifier l'unicité du code au même niveau hiérarchique"""
        for record in self:
            domain = [
                ('code', '=', record.code),
                ('parent_id', '=', record.parent_id.id if record.parent_id else False),
                ('company_id', '=', record.company_id.id),
                ('id', '!=', record.id)
            ]
            existing = self.search(domain)
            if existing:
                raise ValidationError(
                    _('Le code "%s" existe déjà au même niveau hiérarchique.') % record.code
                )
    
    @api.constrains('parent_id')
    def _check_parent_recursion(self):
        """Vérifier qu'il n'y a pas de récursion dans la hiérarchie"""
        if not self._check_recursion():
            raise ValidationError(_('Erreur : vous ne pouvez pas créer de hiérarchie récursive.'))
    
    @api.constrains('parent_id', 'budget_type')
    def _check_budget_type_consistency(self):
        """Vérifier la cohérence du type budgétaire avec le parent"""
        for record in self:
            if record.parent_id and record.parent_id.budget_type != record.budget_type:
                raise ValidationError(
                    _('Le type budgétaire doit être cohérent avec celui du parent.')
                )
    
    @api.constrains('is_analytical', 'child_ids')
    def _check_analytical_consistency(self):
        """Vérifier qu'un poste analytique n'a pas d'enfants"""
        for record in self:
            if record.is_analytical and record.child_ids:
                raise ValidationError(
                    _('Un poste analytique ne peut pas avoir de postes enfants.')
                )
    
    # Méthodes de calcul
    @api.depends('parent_id', 'code')
    def _compute_complete_code(self):
        """Calculer le code complet avec hiérarchie"""
        for record in self:
            if record.parent_id:
                record.complete_code = f"{record.parent_id.complete_code}.{record.code}"
            else:
                record.complete_code = record.code
    
    @api.depends('parent_id')
    def _compute_level(self):
        """Calculer le niveau dans la hiérarchie"""
        for record in self:
            level = 0
            parent = record.parent_id
            while parent:
                level += 1
                parent = parent.parent_id
            record.level = level
    
    @api.depends('child_ids')
    def _compute_has_children(self):
        """Calculer si le poste a des enfants"""
        for record in self:
            record.has_children = bool(record.child_ids)
    
    @api.depends('name', 'code', 'parent_id')
    def _compute_full_name(self):
        """Calculer le nom complet avec hiérarchie"""
        for record in self:
            names = []
            current = record
            while current:
                names.append(f"{current.code} - {current.name}")
                current = current.parent_id
            record.full_name = " / ".join(reversed(names))
    
    # Méthodes d'action
    def action_view_credits(self):
        """Voir les crédits de ce poste"""
        self.ensure_one()
        return {
            'name': _('Crédits budgétaires'),
            'type': 'ir.actions.act_window',
            'res_model': 'budget.credit',
            'view_mode': 'tree,form',
            'domain': [('nomenclature_id', '=', self.id)],
            'context': {'default_nomenclature_id': self.id},
        }
    
    def action_view_engagements(self):
        """Voir les engagements de ce poste"""
        self.ensure_one()
        return {
            'name': _('Engagements'),
            'type': 'ir.actions.act_window',
            'res_model': 'budget.engagement',
            'view_mode': 'tree,form',
            'domain': [('nomenclature_id', '=', self.id)],
            'context': {'default_nomenclature_id': self.id},
        }
    
    @api.model
    def get_analytical_positions(self, budget_type='expense', company_id=None):
        """Obtenir les postes analytiques pour un type donné"""
        if not company_id:
            company_id = self.env.company.id
        
        return self.search([
            ('is_analytical', '=', True),
            ('budget_type', '=', budget_type),
            ('company_id', '=', company_id),
            ('active', '=', True),
        ])
    
    def get_children_recursive(self):
        """Obtenir tous les enfants récursivement"""
        children = self.env['budget.nomenclature']
        for record in self:
            children |= record.child_ids
            if record.child_ids:
                children |= record.child_ids.get_children_recursive()
        return children
    
    def get_analytical_children(self):
        """Obtenir tous les postes analytiques enfants"""
        all_children = self.get_children_recursive()
        return all_children.filtered('is_analytical')
    
    def name_get(self):
        """Personnaliser l'affichage du nom"""
        result = []
        for record in self:
            name = f"{record.complete_code} - {record.name}"
            result.append((record.id, name))
        return result
    
    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        """Recherche personnalisée par code ou nom"""
        args = args or []
        if name:
            records = self.search([
                '|', ('code', operator, name),
                ('name', operator, name)
            ] + args, limit=limit)
        else:
            records = self.search(args, limit=limit)
        return records.name_get()
