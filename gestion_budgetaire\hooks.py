# -*- coding: utf-8 -*-

import logging

_logger = logging.getLogger(__name__)


def post_init_hook(env):
    """Hook exécuté après l'installation du module"""

    _logger.info('Executing post-installation hook for gestion_budgetaire')

    # Créer les données de base si elles n'existent pas
    _create_default_data(env)

    # Configurer les paramètres par défaut
    _set_default_parameters(env)

    # Créer les séquences manquantes
    _create_missing_sequences(env)

    _logger.info('Post-installation hook completed successfully')


def uninstall_hook(env):
    """Hook exécuté avant la désinstallation du module"""

    _logger.info('Executing uninstall hook for gestion_budgetaire')

    # Nettoyer les données spécifiques au module
    _cleanup_module_data(env)

    _logger.info('Uninstall hook completed successfully')


def _create_default_data(env):
    """Créer les données par défaut nécessaires"""
    try:
        # Vérifier et créer les types d'engagement par défaut
        default_engagement_types = [
            {
                'name': 'Prise en charge',
                'code': 'PEC',
                'budget_impact': 'consumption',
                'requires_validation': True,
                'sequence': 10,
            },
            {
                'name': 'Dépense',
                'code': 'DEP',
                'budget_impact': 'consumption',
                'requires_validation': True,
                'auto_create_mandate': True,
                'sequence': 20,
            },
        ]

        for type_data in default_engagement_types:
            existing = env['budget.engagement.type'].search([
                ('code', '=', type_data['code'])
            ], limit=1)

            if not existing:
                env['budget.engagement.type'].create(type_data)
                _logger.info('Created default engagement type: %s', type_data['name'])

        # Vérifier et créer les types de paiement par défaut
        default_payment_types = [
            {
                'name': 'CCP (Compte Courant Postal)',
                'code': 'CCP',
                'requires_bank_account': True,
                'is_electronic': True,
                'sequence': 10,
            },
            {
                'name': 'Trésor Public',
                'code': 'TRE',
                'requires_bank_account': False,
                'is_electronic': False,
                'sequence': 20,
            },
        ]

        for type_data in default_payment_types:
            existing = env['budget.payment.type'].search([
                ('code', '=', type_data['code'])
            ], limit=1)

            if not existing:
                env['budget.payment.type'].create(type_data)
                _logger.info('Created default payment type: %s', type_data['name'])

    except Exception as e:
        _logger.error('Error creating default data: %s', str(e))


def _set_default_parameters(env):
    """Configurer les paramètres par défaut"""
    try:
        # Paramètres de configuration par défaut
        default_params = {
            'gestion_budgetaire.check_budget_on_purchase_line': 'False',
            'gestion_budgetaire.auto_validate_purchase_engagement': 'False',
            'gestion_budgetaire.auto_create_mandate': 'False',
            'gestion_budgetaire.analytic_integration': 'True',
            'gestion_budgetaire.auto_create_perception_title': 'False',
            'gestion_budgetaire.default_alert_threshold': '80.0',
            'gestion_budgetaire.default_blocking_threshold': '100.0',
        }

        for param_key, param_value in default_params.items():
            existing = env['ir.config_parameter'].search([
                ('key', '=', param_key)
            ], limit=1)

            if not existing:
                env['ir.config_parameter'].create({
                    'key': param_key,
                    'value': param_value,
                })
                _logger.info('Set default parameter: %s = %s', param_key, param_value)

    except Exception as e:
        _logger.error('Error setting default parameters: %s', str(e))


def _create_missing_sequences(env):
    """Créer les séquences manquantes"""
    try:
        # Séquences requises
        required_sequences = [
            {
                'name': 'Engagement Budgétaire',
                'code': 'budget.engagement',
                'prefix': 'ENG/%(year)s/',
                'padding': 5,
            },
            {
                'name': 'Mandat de Paiement',
                'code': 'budget.mandate',
                'prefix': 'MAN/%(year)s/',
                'padding': 5,
            },
            {
                'name': 'Titre de Perception',
                'code': 'budget.perception.title',
                'prefix': 'TP/%(year)s/',
                'padding': 5,
            },
            {
                'name': 'Encaissement',
                'code': 'budget.perception.collection',
                'prefix': 'ENC/%(year)s/',
                'padding': 5,
            },
        ]

        for seq_data in required_sequences:
            existing = env['ir.sequence'].search([
                ('code', '=', seq_data['code'])
            ], limit=1)

            if not existing:
                env['ir.sequence'].create({
                    'name': seq_data['name'],
                    'code': seq_data['code'],
                    'prefix': seq_data['prefix'],
                    'padding': seq_data['padding'],
                    'number_next': 1,
                    'number_increment': 1,
                    'use_date_range': True,
                })
                _logger.info('Created sequence: %s', seq_data['name'])

    except Exception as e:
        _logger.error('Error creating sequences: %s', str(e))


def _cleanup_module_data(env):
    """Nettoyer les données du module avant désinstallation"""
    try:
        # Supprimer les paramètres de configuration
        config_params = env['ir.config_parameter'].search([
            ('key', 'like', 'gestion_budgetaire.%')
        ])
        if config_params:
            config_params.unlink()
            _logger.info('Removed %d configuration parameters', len(config_params))

        # Supprimer les séquences créées par le module
        sequences_to_remove = [
            'budget.engagement',
            'budget.mandate',
            'budget.perception.title',
            'budget.perception.collection',
        ]

        for seq_code in sequences_to_remove:
            sequence = env['ir.sequence'].search([('code', '=', seq_code)])
            if sequence:
                sequence.unlink()
                _logger.info('Removed sequence: %s', seq_code)

        # Note: Les données métier (exercices, engagements, etc.) ne sont pas supprimées
        # pour préserver l'historique budgétaire

    except Exception as e:
        _logger.error('Error during cleanup: %s', str(e))
