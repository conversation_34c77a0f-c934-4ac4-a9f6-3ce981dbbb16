/* Styles CSS pour le module Gestion Budgétaire */

/* Couleurs principales */
:root {
    --budget-primary: #2E7D32;
    --budget-secondary: #1976D2;
    --budget-success: #388E3C;
    --budget-warning: #F57C00;
    --budget-danger: #D32F2F;
    --budget-info: #0288D1;
    --budget-light: #F5F5F5;
    --budget-dark: #424242;
}

/* Styles pour les badges d'état */
.badge-budget-draft {
    background-color: var(--budget-info);
    color: white;
}

.badge-budget-validated {
    background-color: var(--budget-success);
    color: white;
}

.badge-budget-blocked {
    background-color: var(--budget-danger);
    color: white;
}

.badge-budget-waiting {
    background-color: var(--budget-warning);
    color: white;
}

/* Styles pour les indicateurs budgétaires */
.budget-indicator {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
}

.budget-indicator.available {
    background-color: #E8F5E8;
    color: var(--budget-success);
    border: 1px solid #C8E6C9;
}

.budget-indicator.alert {
    background-color: #FFF3E0;
    color: var(--budget-warning);
    border: 1px solid #FFE0B2;
}

.budget-indicator.exceeded {
    background-color: #FFEBEE;
    color: var(--budget-danger);
    border: 1px solid #FFCDD2;
}

/* Styles pour les barres de progression budgétaire */
.budget-progress {
    height: 20px;
    background-color: #E0E0E0;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.budget-progress-bar {
    height: 100%;
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    font-weight: 500;
}

.budget-progress-bar.normal {
    background-color: var(--budget-success);
}

.budget-progress-bar.warning {
    background-color: var(--budget-warning);
}

.budget-progress-bar.danger {
    background-color: var(--budget-danger);
}

/* Styles pour les cartes budgétaires */
.budget-card {
    border: 1px solid #E0E0E0;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.budget-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.budget-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #E0E0E0;
}

.budget-card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--budget-dark);
    margin: 0;
}

.budget-card-subtitle {
    font-size: 0.875rem;
    color: #757575;
    margin: 0;
}

.budget-card-body {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.budget-amount {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
}

.budget-amount-label {
    font-size: 0.875rem;
    color: #757575;
}

.budget-amount-value {
    font-size: 1rem;
    font-weight: 600;
    color: var(--budget-dark);
}

.budget-amount-value.positive {
    color: var(--budget-success);
}

.budget-amount-value.negative {
    color: var(--budget-danger);
}

/* Styles pour les tableaux budgétaires */
.budget-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 16px;
}

.budget-table th,
.budget-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #E0E0E0;
}

.budget-table th {
    background-color: var(--budget-light);
    font-weight: 600;
    color: var(--budget-dark);
}

.budget-table tr:hover {
    background-color: #F9F9F9;
}

.budget-table .text-right {
    text-align: right;
}

.budget-table .text-center {
    text-align: center;
}

/* Styles pour les formulaires budgétaires */
.budget-form-section {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid #E0E0E0;
    border-radius: 8px;
    background-color: #FAFAFA;
}

.budget-form-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--budget-primary);
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--budget-primary);
}

/* Styles pour les alertes budgétaires */
.budget-alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.budget-alert.info {
    background-color: #E3F2FD;
    border-left: 4px solid var(--budget-info);
    color: #0D47A1;
}

.budget-alert.warning {
    background-color: #FFF8E1;
    border-left: 4px solid var(--budget-warning);
    color: #E65100;
}

.budget-alert.danger {
    background-color: #FFEBEE;
    border-left: 4px solid var(--budget-danger);
    color: #B71C1C;
}

.budget-alert.success {
    background-color: #E8F5E8;
    border-left: 4px solid var(--budget-success);
    color: #1B5E20;
}

/* Styles pour les boutons budgétaires */
.btn-budget-primary {
    background-color: var(--budget-primary);
    border-color: var(--budget-primary);
    color: white;
}

.btn-budget-primary:hover {
    background-color: #1B5E20;
    border-color: #1B5E20;
}

.btn-budget-secondary {
    background-color: var(--budget-secondary);
    border-color: var(--budget-secondary);
    color: white;
}

.btn-budget-secondary:hover {
    background-color: #1565C0;
    border-color: #1565C0;
}

/* Styles responsives */
@media (max-width: 768px) {
    .budget-card {
        padding: 12px;
    }
    
    .budget-card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .budget-amount {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .budget-table {
        font-size: 0.875rem;
    }
    
    .budget-table th,
    .budget-table td {
        padding: 6px 8px;
    }
}

/* Animations */
@keyframes budget-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.budget-pulse {
    animation: budget-pulse 2s infinite;
}

/* Styles pour les icônes */
.budget-icon {
    width: 20px;
    height: 20px;
    display: inline-block;
    vertical-align: middle;
}

.budget-icon.large {
    width: 32px;
    height: 32px;
}

/* Styles pour les tooltips budgétaires */
.budget-tooltip {
    position: relative;
    cursor: help;
}

.budget-tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--budget-dark);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.875rem;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}
