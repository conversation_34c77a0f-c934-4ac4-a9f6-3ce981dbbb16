# -*- coding: utf-8 -*-

from odoo import api, fields, models


class ResConfigSettings(models.TransientModel):
    """Extension des paramètres de configuration pour la gestion budgétaire"""
    
    _inherit = 'res.config.settings'
    
    # Contrôle budgétaire
    budget_check_on_purchase_line = fields.Bo<PERSON>an(
        string='Contrôle budgétaire sur les lignes d\'achat',
        config_parameter='gestion_budgetaire.check_budget_on_purchase_line',
        help='Vérifier la disponibilité budgétaire lors de la saisie des lignes de commande d\'achat'
    )
    
    budget_auto_validate_purchase_engagement = fields.Boolean(
        string='Validation automatique des engagements d\'achat',
        config_parameter='gestion_budgetaire.auto_validate_purchase_engagement',
        help='Valider automatiquement les engagements créés depuis les commandes d\'achat'
    )
    
    budget_auto_create_mandate = fields.<PERSON><PERSON><PERSON>(
        string='Création automatique des mandats',
        config_parameter='gestion_budgetaire.auto_create_mandate',
        help='Créer automatiquement les mandats lors de la validation des factures fournisseurs'
    )
    
    # Intégration
    budget_analytic_integration = fields.<PERSON><PERSON><PERSON>(
        string='Intégration avec la comptabilité analytique',
        config_parameter='gestion_budgetaire.analytic_integration',
        help='Synchroniser les postes budgétaires avec les comptes analytiques'
    )
    
    budget_auto_create_perception_title = fields.Boolean(
        string='Génération automatique des titres de perception',
        config_parameter='gestion_budgetaire.auto_create_perception_title',
        help='Créer automatiquement les titres de perception depuis les factures clients'
    )
    
    # Seuils et alertes
    budget_default_alert_threshold = fields.Float(
        string='Seuil d\'alerte par défaut (%)',
        config_parameter='gestion_budgetaire.default_alert_threshold',
        default=80.0,
        help='Seuil de consommation budgétaire déclenchant une alerte'
    )
    
    budget_default_blocking_threshold = fields.Float(
        string='Seuil de blocage par défaut (%)',
        config_parameter='gestion_budgetaire.default_blocking_threshold',
        default=100.0,
        help='Seuil de consommation budgétaire déclenchant un blocage automatique'
    )
    
    @api.constrains('budget_default_alert_threshold', 'budget_default_blocking_threshold')
    def _check_thresholds(self):
        """Vérifier la cohérence des seuils"""
        for record in self:
            if record.budget_default_alert_threshold < 0 or record.budget_default_alert_threshold > 100:
                raise ValidationError(_('Le seuil d\'alerte doit être compris entre 0 et 100%.'))
            
            if record.budget_default_blocking_threshold < 0 or record.budget_default_blocking_threshold > 200:
                raise ValidationError(_('Le seuil de blocage doit être compris entre 0 et 200%.'))
            
            if record.budget_default_alert_threshold > record.budget_default_blocking_threshold:
                raise ValidationError(_('Le seuil d\'alerte ne peut pas être supérieur au seuil de blocage.'))
