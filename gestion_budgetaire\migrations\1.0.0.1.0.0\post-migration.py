# -*- coding: utf-8 -*-

import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)


def migrate(cr, version):
    """Post-migration script for gestion_budgetaire module"""
    
    _logger.info('Starting post-migration of gestion_budgetaire from %s to 1.0.0', version)
    
    env = api.Environment(cr, SUPERUSER_ID, {})
    
    # Recalculer les montants après migration
    _recalculate_amounts(env)
    
    # Vérifier l'intégrité des données
    _check_data_integrity(env)
    
    # Mettre à jour les permissions
    _update_permissions(env)
    
    _logger.info('Post-migration of gestion_budgetaire completed successfully')


def _recalculate_amounts(env):
    """Recalculer tous les montants après migration"""
    try:
        # Recalculer les montants des crédits
        credits = env['budget.credit'].search([])
        for credit in credits:
            credit._compute_amounts()
            _logger.info('Recalculated amounts for credit %s', credit.name)
        
        # Recalculer les montants des engagements
        engagements = env['budget.engagement'].search([])
        for engagement in engagements:
            engagement._compute_amounts()
            _logger.info('Recalculated amounts for engagement %s', engagement.name)
        
        # Recalculer les totaux des exercices
        exercises = env['budget.exercise'].search([])
        for exercise in exercises:
            exercise._compute_totals()
            _logger.info('Recalculated totals for exercise %s', exercise.name)
            
    except Exception as e:
        _logger.error('Error recalculating amounts: %s', str(e))


def _check_data_integrity(env):
    """Vérifier l'intégrité des données après migration"""
    try:
        # Vérifier les crédits sans nomenclature analytique
        invalid_credits = env['budget.credit'].search([
            ('nomenclature_id.is_analytical', '=', False)
        ])
        if invalid_credits:
            _logger.warning('Found %d credits with non-analytical nomenclature', len(invalid_credits))
        
        # Vérifier les engagements sans crédit
        engagements_without_credit = env['budget.engagement'].search([
            ('credit_id', '=', False),
            ('state', '!=', 'cancelled')
        ])
        if engagements_without_credit:
            _logger.warning('Found %d engagements without credit', len(engagements_without_credit))
        
        # Vérifier les exercices sans dates
        exercises_without_dates = env['budget.exercise'].search([
            '|', ('date_start', '=', False), ('date_end', '=', False)
        ])
        if exercises_without_dates:
            _logger.warning('Found %d exercises without proper dates', len(exercises_without_dates))
            
    except Exception as e:
        _logger.error('Error checking data integrity: %s', str(e))


def _update_permissions(env):
    """Mettre à jour les permissions après migration"""
    try:
        # Vérifier que les groupes de sécurité existent
        groups_to_check = [
            'gestion_budgetaire.group_budget_user',
            'gestion_budgetaire.group_budget_agent',
            'gestion_budgetaire.group_budget_controller',
            'gestion_budgetaire.group_budget_manager',
            'gestion_budgetaire.group_budget_admin',
        ]
        
        for group_xml_id in groups_to_check:
            try:
                group = env.ref(group_xml_id)
                _logger.info('Security group %s found with %d users', group.name, len(group.users))
            except ValueError:
                _logger.warning('Security group %s not found', group_xml_id)
        
        # Mettre à jour les règles d'accès si nécessaire
        _logger.info('Security groups and access rules updated successfully')
        
    except Exception as e:
        _logger.error('Error updating permissions: %s', str(e))
